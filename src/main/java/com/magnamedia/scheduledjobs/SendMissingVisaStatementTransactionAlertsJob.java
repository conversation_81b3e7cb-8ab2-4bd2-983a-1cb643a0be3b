package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.VisaStatementTransaction;
import com.magnamedia.extra.VisaStatementTransactionType;
import com.magnamedia.repository.VisaStatementTransactionRepository;
import com.magnamedia.service.MessagingService;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.VisaStatementService;

public class SendMissingVisaStatementTransactionAlertsJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(SendMissingVisaStatementTransactionAlertsJob.class.getName());
    private VisaStatementTransactionRepository transactionRepository;
    private MessagingService messagingService;
    private VisaStatementService visaStatementService;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Job started");
        transactionRepository = Setup.getRepository(VisaStatementTransactionRepository.class);
        messagingService = Setup.getApplicationContext().getBean(MessagingService.class);
        visaStatementService = Setup.getApplicationContext().getBean(VisaStatementService.class);

        sendDailyMissingERPAlerts();

        logger.log(Level.INFO, "Job finished");
    }

    private void sendDailyMissingERPAlerts() {

        List<VisaStatementTransaction> missingRecords = transactionRepository
                .findByTypeAndFinishedFalse(VisaStatementTransactionType.MissingFromERP);

        if (missingRecords.isEmpty()) return;

        // Prepare distinct amounts from missing records
        List<Double> amountsToSearch = missingRecords.stream()
                .map(VisaStatementTransaction::getAmount)
                .distinct()
                .collect(Collectors.toList());

        // Preload all matched records for these amounts in a single query
        List<VisaStatementTransaction> matchedRecords = transactionRepository
                .findByTypeAndAmountsInAndFinishedFalse(VisaStatementTransactionType.Matched, amountsToSearch);

        // Group once for fast lookup
        Map<Double, List<VisaStatementTransaction>> matchedRecordsByAmount = matchedRecords.stream()
                .collect(Collectors.groupingBy(VisaStatementTransaction::getAmount));

        List<VisaStatementTransaction> missingRecordsThreeDaysUnclosed = missingRecords.stream().filter(
                        m -> m.getExpenseCreationDate() != null &&
                                (new Date().getTime() - m.getExpenseCreationDate().getTime()) > 3 * 24 * 60 * 60 * 1000)
                .collect(Collectors.toList());

        if (!missingRecordsThreeDaysUnclosed.isEmpty()) {
            missingRecordsThreeDaysUnclosed.forEach(t -> messagingService.sendEmailToOfficeStaff(
                    "missing_visa_expense_alert",
                    visaStatementService.
                            fillParams(t, matchedRecordsByAmount
                                            .getOrDefault(t.getAmount(), Collections.emptyList())
                                            .stream().filter(
                                                    m -> m.getExpenseCreationDate() != null &&
                                                            (new Date().getTime() - m.getExpenseCreationDate().getTime()) > 3 * 24 * 60 * 60 * 1000)
                                            .collect(Collectors.toList()),
                                    missingRecordsThreeDaysUnclosed.stream().filter(record -> record.getAmount() == t.getAmount()).count()),
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS_AFTER_THREE_DAYS_UNCLOSED),
                    "ACTION NEEDED: A Transaction is Missing in Visa Expenses"));
        }

        missingRecords.forEach(t -> messagingService.sendEmailToOfficeStaff(
                "missing_visa_expense_alert",
                visaStatementService.
                        fillParams(t, matchedRecordsByAmount
                                        .getOrDefault(t.getAmount(), Collections.emptyList()),
                                missingRecords.stream().filter(record -> record.getAmount() == t.getAmount()).count()),
                Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS),
                "ACTION NEEDED: A Transaction is Missing in Visa Expenses"));
    }
}