/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.serializer.HousemaidIdLabelSalaryFoodHousingSerializer;
import com.magnamedia.module.type.PayrollExceptionType;

import javax.persistence.FetchType;
import javax.persistence.OneToOne;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class PayrollException {
 
    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = HousemaidIdLabelSalaryFoodHousingSerializer.class)
    private Housemaid housemaid;
    private PayrollExceptionType type;
    private Double montlyPayment;

    public PayrollException() {
    }

    public PayrollException(Housemaid housemaid, PayrollExceptionType type) {
        this.housemaid = housemaid;
        this.type = type;
    }

    public PayrollException(Housemaid housemaid, PayrollExceptionType type, Double montlyPayment) {
        this.housemaid = housemaid;
        this.type = type;
        this.montlyPayment = montlyPayment;
    }
    
    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

 

    public PayrollExceptionType getType() {
        return type;
    }

    public void setType(PayrollExceptionType type) {
        this.type = type;
    }

    public Double getMontlyPayment() {
        return montlyPayment;
    }

    public void setMontlyPayment(Double montlyPayment) {
        this.montlyPayment = montlyPayment;
    }
    
    
    
}
