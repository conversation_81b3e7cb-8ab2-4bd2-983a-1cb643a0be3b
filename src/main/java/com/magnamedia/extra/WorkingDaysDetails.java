/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.magnamedia.core.type.HousemaidStatus;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class WorkingDaysDetails {

    Integer totalWorkingDays;

    List<WorkingDaysPeriod> workingPeriods;

    public WorkingDaysDetails() {
    }

    public WorkingDaysDetails(Integer totalWorkingDays,
            List<WorkingDaysPeriod> workingPeriods) {
        this.totalWorkingDays = totalWorkingDays;
        this.workingPeriods = workingPeriods;
    }

    public Integer getTotalWorkingDays() {
        return totalWorkingDays;
    }

    public void setTotalWorkingDays(Integer totalWorkingDays) {
        this.totalWorkingDays = totalWorkingDays;
    }

    public List<WorkingDaysPeriod> getWorkingPeriods() {
        return workingPeriods;
    }

    public void setWorkingPeriods(List<WorkingDaysPeriod> workingPeriods) {
        this.workingPeriods = workingPeriods;
    }



}
