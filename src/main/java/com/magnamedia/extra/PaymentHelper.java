package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.WordTemplate;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractPackageType;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.CalculateDiscountsWithVatService;
import com.magnamedia.service.ClientMessagingAndRefundService;
import com.magnamedia.service.WordTemplateService;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Nov 25, 2020
 *         Jirra ACC-2812
 */

public class PaymentHelper {
    private static final Logger logger = Logger.getLogger(PaymentHelper.class.getSimpleName());
    private static final String prefix = "MMM ";

    public static final DecimalFormat df = new DecimalFormat("###,###,###");
    public static final DecimalFormat df_two_decimal = new DecimalFormat("###,###,###.##");

    public static InputStream generateTaxInvoice(Contract contract, ContractPayment contractPayment) {
        ContractPaymentTerm activeCPT = contract.getActiveContractPaymentTerm();
        WordTemplate template = activeCPT.getTaxInvoiceTemplate();
//        Utils utils = Setup.getApplicationContext().getBean(Utils.class);

        if (template == null) {
            throw new RuntimeException("There is no suitable word template");
        }

        WordTemplateService wordTemplateService = Setup.getApplicationContext().getBean(WordTemplateService.class);
        Map<String, Object> parameters = getTaxInvoiceParameters(contract, activeCPT);

        InputStream in = null;
        try {
            List<StandardEvaluationContext> payments = new ArrayList<>();
            // add matching fees payment type in case app created via hiring link
//            boolean isMaidVisa = activeCPT.getContract().getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE));

//            addDefaultMatchingFeePaymentType(app, contractPaymentTypes);

            if (contractPayment != null) {
                HashMap paymentInfo = new HashMap();
                paymentInfo.put("description", contractPayment.getDescription());
                Double amount = contractPayment.getAmount();
                if (BooleanUtils.toBoolean(contractPayment.getIncludeWorkerSalary()) && contract.getWorkerSalaryWithoutVat() != null && !contract.isWorkerSalaryVatted()) {
                    amount -= contract.getWorkerSalaryWithoutVat();
                }

                paymentInfo.put("amount", Double.parseDouble(String.format("%.0f", Math.floor(DiscountsWithVatHelper.getAmountWithoutVat(amount)))));
                paymentInfo.put("totalAmount", Double.parseDouble(String.format("%.0f", amount)));
                payments.add(new StandardEvaluationContext(paymentInfo));
            }

            XWPFDocument document = WordDocumentHelper.parseTables(Storage.getStream(template.getAttachment("template")), payments, parameters);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            document.write(out);
            InputStream inputStream = new ByteArrayInputStream(out.toByteArray());
            in = wordTemplateService.generateDocument(inputStream, parameters);

        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }

        return in;
    }

    private static Map<String, Object> getTaxInvoiceParameters(Contract contract, ContractPaymentTerm cpt) {
        Utils utils = Setup.getApplicationContext().getBean(Utils.class);

        Map<String, Object> parameters = new HashMap();
        Client client = contract != null ? contract.getClient() : null;
        Housemaid housemaid = contract != null ? contract.getHousemaid() : null;
        PicklistItem nationality = housemaid != null ? housemaid.getNationality() : null;

        parameters.put("trn", "");
        parameters.put("invoice_id", "");
        parameters.put("invoice_date", DateUtil.formatNotDashedFullDate(new Date()));
        parameters.put("client_name", client != null && client.getName() != null ? client.getName() : "");
        parameters.put("client_address", client.getFullAddress() != null ? client.getFullAddress() : "");
        parameters.put("client_mobile", client.getNormalizedMobileNumber() != null ? client.getNormalizedMobileNumber() : "");
        parameters.put("maid_nationality_en", nationality != null ? nationality.getName() : "");
        parameters.put("maid_nationality_ar", nationality == null ? "" : nationality.hasTag("arabic_label") ? nationality.getTagValue("arabic_label").getValue() : "");
        parameters.put("maid_name", "");

        Double higherPriceBound = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CONTRACT_PAYMENTS_RECEIPT_HIGHER_PRICE_BOUND));
        Double generalDiscount = higherPriceBound - cpt.getMonthlyPaymentWithoutVat();
        //Jirra ACC-2897
        parameters.put("general_discount", utils.getWordTemplateParamValue(generalDiscount));
        parameters.put("higher_price_bound", utils.getWordTemplateParamValue(higherPriceBound));
        parameters.put("maid_type_en", nationality.getCode().equals("philippines") ? "Nanny" : "Maid");
        parameters.put("maid_type_ar", nationality.getCode().equals("philippines") ? "مربية أطفال" : "خادمة");
        parameters.put("description", "Service Fee");
        // ACC-5759
        if (contract.isMaidVisa()) {
            parameters.put("package_ar", contract.getMvContractPackage() == null ||
                    contract.getMvContractPackage().equals(MVPackageType.FLEXIBLE) ? "المرنة" : "المؤقتة");
            parameters.put("package_en", contract.getMvContractPackage() == null ||
                    contract.getMvContractPackage().equals(MVPackageType.FLEXIBLE) ? "Flexible" : "Temporary");
        }

        try {
            Map<String, Double> amounts =  PaymentReceiptHelper.getPaymentsBreakdownWeeklyMonthlyAmount(cpt.getPaymentTermConfig());

            Double weeklyAmount = amounts.get("weeklyAmount");
            Double totalAmount = amounts.get("totalAmount");

            if (weeklyAmount != null) {
                parameters.put("payments_breakdown_weekly_amount", utils.getWordTemplateParamValue(weeklyAmount));
            }
            if (totalAmount != null) {
                parameters.put("payments_breakdown_monthly_amount", utils.getWordTemplateParamValue(totalAmount));
            }

        } catch (Exception ex) {
            logger.log(Level.SEVERE, "ERROR: " + ex.getMessage(), ex);
        }
        // ACC-6977
        parameters.put("live_out_amount", utils.getWordTemplateParamValue(Optional.ofNullable(cpt.getLiveOutAmount()).orElse(0.0), true));


        return parameters;
    }

    public static SelectFilter getSelectFilter(FilterItem filterItem) {
        SelectFilter selectFilter = new SelectFilter();

        if (filterItem.getProperty() != null && filterItem.getProperty().equalsIgnoreCase("directDebitFile.id")) {
            filterItem.setProperty("directDebitFileId");
        }

        if (filterItem.getProperty() != null && filterItem.getProperty().equalsIgnoreCase("directDebitFile.applicationId")) {
            SelectQuery ddfQuery = new SelectQuery(DirectDebitFile.class);
            ddfQuery.filterBy("applicationId", filterItem.getOperation(), filterItem.getValue());
            List<DirectDebitFile> ddfs = ddfQuery.execute();
            if (ddfs == null) return null;
            List<Long> ddfIDs = ddfs.stream().map(ddf -> ddf.getId()).collect(Collectors.toList());
            selectFilter = new SelectFilter("directDebitFileId", "in", ddfIDs);
        } else {
            selectFilter = selectFilter.and(filterItem.getSelectFilter(Payment.class));
        }

        return selectFilter;
    }

    public static String getDescription(Contract contract, ContractPayment contractPayment) {
        Boolean isMaidCC = contract.getContractProspectType().getCode()
                .equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE));

        switch (contractPayment.getPaymentType().getCode()) {
            case "monthly_payment":
                return getPackage(contractPayment);
            case "same_day_recruitment_fee":
            case "employing_ex_dh":
                return isMaidCC ? "Non-refundable employee settlement" :  "Travel Fee";
            case "accommodation_fee":
                return "Company Accommodation Fee";
            case "day-to-day_extension":
                return "Flexible Package Fee";
            case "deposit":
                return "Deposit";
            case "service_charge":
                return "Service Charge";
            case "upgrading_nationality":
                return "Upgrading Nationality";
        }
        return "";
    }

    public static String getPackage(ContractPayment contractPayment) {
        ContractPaymentTerm cpt = contractPayment.getContractPaymentTerm();
        if (cpt != null && cpt.getPackageType() != null
                && (cpt.getPackageType().equals(ContractPackageType.PROBATION_PACKAGE)
                || cpt.getPackageType().equals(ContractPackageType.TEMPORARY_PACKAGE))) {
            return cpt.getPackageType().getLabel() + " Fee";
        } else
            return "Flexible Package Fee";
    }

    public static boolean isChangedToReceived(Payment payment) {
        return isChangedTo(payment, PaymentStatus.RECEIVED);
    }

    public static boolean isChangedToBounced(Payment payment) {
        return isChangedTo(payment, PaymentStatus.BOUNCED);
    }

    public static boolean isBouncedAgain(Payment payment) {
        Payment old = getOldPayment(payment, "trials");
        logger.info("old is null: " + (old == null) + "; id: " + payment.getId());
        if (old == null) return false;

        logger.info("old trials: " + old.getTrials());
        logger.info("new trials: "+ payment.getTrials() + "; status: " + payment.getStatus());

        if (payment.getStatus() == null) return false;

        return payment.getStatus().equals(PaymentStatus.BOUNCED) &&
                !old.getTrials().equals(payment.getTrials());
    }

    public static boolean isChangedTo(Payment payment, PaymentStatus paymentStatus) {
        Payment old = getOldPayment(payment, "status");
        if (payment.getStatus() == null) return false;

        return payment.getStatus().equals(paymentStatus) &&
                (old == null || old.getStatus() == null || !old.getStatus().equals(paymentStatus));
    }

    public static boolean isChangedToReplaced(Payment payment) {
        Payment old = getOldPayment(payment, "replaced");
        return old != null &&
                payment.getStatus().equals(PaymentStatus.BOUNCED) &&
                BooleanUtils.toBoolean(payment.getReplaced()) &&
                !BooleanUtils.toBoolean(old.getReplaced());
    }

    public static Payment getOldPayment(Payment payment, String modifiedColumn) {
        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery(Payment.class);
        historyQuery.filterBy("id", "=", payment.getId());
        historyQuery.filterByChanged(modifiedColumn);
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);

        List<Payment> oldPayments = historyQuery.execute();

        if (oldPayments == null || oldPayments.isEmpty()) return null;

        logger.info(prefix + "old payment found");
        Payment old = oldPayments.get(0);
        return old;
    }

    public static String getCC_APP_PaymentVatDescription(Payment payment, boolean clientPayingVat) {
        double fullAmount = Math.floor(payment.getAmountOfPayment());

        return getCC_APP_PaymentVatDescription(fullAmount, payment.getPaymentWithoutVAT(), clientPayingVat);

    }

    public static String getCC_APP_PaymentVatDescription(ContractPayment payment, boolean clientPayingVat) {
        double fullAmount = Math.floor(payment.getAmount());

        return getCC_APP_PaymentVatDescription(fullAmount, payment.getPaymentWithoutVAT(), clientPayingVat);
    }

    public static String getPaymentDescriptionForCCAPP(ContractPayment cp) {
        Date today = new DateTime().withTimeAtStartOfDay().toDate();
        if (new DateTime(cp.getDate()).withTimeAtStartOfDay().toDate().equals(today)) {
            return "Today";
        }

        return DateUtil.formatCCAPPDate(cp.getDate());
    }

    private static String getCC_APP_PaymentVatDescription(double fullAmount, Double amountWithoutVAT, boolean clientPayingVat) {
        return new StringBuilder().append(" AED ")
                .append(df.format(fullAmount))
                .append("/month")
                .append(clientPayingVat ? " (" + df.format(amountWithoutVAT) + "<span style='font-size: 14px'> +vat</span>)" : "")
                .append(".")
                .toString();
    }

    public static Payment getPaymentOfDD(Long directDebitId, Date date) {
        DirectDebit directDebit = Setup.getRepository(DirectDebitRepository.class).findOne(directDebitId);
        if (directDebit.getCategory().equals(DirectDebitCategory.A)) {
            return getPaymentOfDDA(directDebitId);
        } else if (directDebit.getCategory().equals(DirectDebitCategory.B)) {
            return getPaymentOfDDB(directDebitId, date);
        }

        return null;
    }

    public static Payment getPaymentOfDDA(Long directDebitId) {
        List<Payment> ddPayments = getPaymentsOfDD(directDebitId);
        if (ddPayments == null || ddPayments.isEmpty()) return null;

        Payment monthly = ddPayments.stream()
                .filter(p -> p.getTypeOfPayment().getCode().equals("monthly_payment"))
                .findFirst().orElse(null);

        if(monthly != null) return monthly;

        return ddPayments.get(0);
    }

    public static Payment getPaymentOfDDB(Long directDebitId, Date date) {
        List<Payment> ddPayments = getPaymentsOfDD(directDebitId);
        if (ddPayments == null || ddPayments.isEmpty()) return null;

        for (Payment payment : ddPayments) {
            if (doesPaymentCoverDate(payment, date)) {
                return payment;
            }
        }

        return null;
    }

    public static List<Payment> getPaymentsOfDD(Long directDebitId) {
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);

        return paymentRepository.findByDirectDebitId(directDebitId);
    }

    public static Payment getDDPaymentOfCPTByDate(ContractPaymentTerm contractPaymentTerm, Date date, PicklistItem type) {
        List<DirectDebit> cptDDs = Setup.getRepository(DirectDebitRepository.class).findByContractPaymentTerm(contractPaymentTerm);
        logger.log(Level.SEVERE, "cptDDs Size: " + cptDDs.size());

        if (cptDDs != null) {
            for (DirectDebit directDebit : cptDDs) {

                //ACC-3954 check that the related direct debit is not canceled or expired according to her category
                if (directDebit.getCategory() != null
                        && (directDebit.getCategory().equals(DirectDebitCategory.A) && directDebit.getMStatus() != null && (directDebit.getMStatus().equals(DirectDebitStatus.CANCELED) || directDebit.getMStatus().equals(DirectDebitStatus.EXPIRED)))
                        || ( directDebit.getCategory().equals(DirectDebitCategory.B) && directDebit.getStatus() != null && (directDebit.getStatus().equals(DirectDebitStatus.CANCELED) || directDebit.getStatus().equals(DirectDebitStatus.EXPIRED))))
                    continue;

                logger.log(Level.SEVERE, "directDebit ID#" + directDebit.getId());

                Payment payment = null;

                if (directDebit.getCategory().equals(DirectDebitCategory.B)) {
                    payment = getPaymentOfDDB(directDebit.getId(), date);
                } else if (directDebit.getCategory().equals(DirectDebitCategory.A)) {
                    payment = getPaymentOfDDA(directDebit.getId());
                }

                if (payment != null) {
                    logger.log(Level.SEVERE, "Payment ID#" + payment.getId());
                    logger.log(Level.SEVERE, "Payment Date" + payment.getDateOfPayment());
                    logger.log(Level.SEVERE, "Payment Type" + payment.getTypeOfPayment().getCode());
                    logger.log(Level.SEVERE, "Payment Statue" + payment.getStatus());
                    logger.log(Level.SEVERE, "Payment Replaced" + payment.getReplaced());

                    if (doesPaymentCoverDate(payment, date) && payment.getTypeOfPayment().getId().equals(type.getId())) {
                        logger.log(Level.SEVERE, "Payment Match");
                        return payment;
                    }
                }

            }
        }

        logger.log(Level.SEVERE, "No Match");
        return null;
    }

    public static boolean doesPaymentCoverDate(Payment payment, Date date) {
        DateTime fromDate = new DateTime(date).dayOfMonth().withMinimumValue().withTimeAtStartOfDay();
        DateTime toDate = new DateTime(date).dayOfMonth().withMaximumValue().withTimeAtStartOfDay();

        if ((payment.getDateOfPayment().after(fromDate.toDate()) || payment.getDateOfPayment().equals(fromDate.toDate())) &&
                (payment.getDateOfPayment().before(toDate.toDate()) || payment.getDateOfPayment().equals(toDate.toDate()))) {
            return true;
        }

        boolean isProPlusMonth = payment.getIsProRated() != null && payment.getIsProRated() &&
                payment.getContract() != null && payment.getContract().getProRatedPlusMonth() != null && payment.getContract().getProRatedPlusMonth();
        if (isProPlusMonth &&
                ((payment.getDateOfPayment().after(fromDate.minusMonths(1).toDate()) || payment.getDateOfPayment().equals(fromDate.minusMonths(1).toDate())) &&
                        (payment.getDateOfPayment().before(toDate.toDate()) || payment.getDateOfPayment().equals(toDate.toDate())))) {
            return true;
        }

        return false;
    }

    public static DirectDebit getMainReplacedDD(Payment entity) {
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);

        DirectDebit directDebit = null;
        if (entity.getReplacementFor() != null && entity.getReplacementFor().getId() != null) {
            Payment replacedPayment = getReplacedPayment(entity);

            directDebit = replacedPayment.getDirectDebit();
        } else if (entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null) {
            directDebit = directDebitRepository.findOne(entity.getDirectDebit().getId());
        }

        return directDebit;
    }

    public static Payment getReplacedPayment(Payment payment) {
        if (payment.getReplacementFor() == null || payment.getReplacementFor().getId() == null) return null;

        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        Payment replacedPayment = paymentRepository.findOne(payment.getReplacementFor().getId());
        while (replacedPayment.getReplacementFor() != null) {
            replacedPayment = replacedPayment.getReplacementFor();
        }

        return replacedPayment;
    }

    public static Double doFullRefund(
            Contract contract, Client client, Payment payment,
            Double amount, boolean groupInOneMessage) {

        Map map = Setup.getApplicationContext()
                .getBean(ClientMessagingAndRefundService.class)
                .fullRefundProcess(contract, client, amount, payment.getClientInformedAboutRefund(), groupInOneMessage, payment);

        payment.setRefundSentToExpensify((Boolean) map.get("refundSentToExpensify"));
        payment.setFullyRefunded((Boolean) map.get("refundSentToExpensify"));
        payment.setClientInformedAboutRefund((Boolean) map.get("clientInformedAboutRefund"));

        return (Double) map.getOrDefault("amountToRefund", 0.0);
    }

    public static boolean isMonthlyPayment(PicklistItem p) {
        if (p.getCode() != null) return p.getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        return p.getId() != null && p.getId()
                .equals(PicklistHelper.getItem(
                        "TypeOfPayment", AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE).getId());
    }

    public static boolean isPaymentDateEqualsDiscountStartDate(ContractPaymentTerm cpt, Date d) {
        DateTime discountedPeriodStartDate = new DateTime(
                Setup.getApplicationContext()
                        .getBean(CalculateDiscountsWithVatService.class)
                        .getDiscountStartDateInMillis(cpt));

        return new DateTime(d).toString("yyyy-MM")
                .equals(discountedPeriodStartDate.toString("yyyy-MM"));
    }
}
