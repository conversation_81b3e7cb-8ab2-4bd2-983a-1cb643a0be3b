package com.magnamedia.extra;

import java.util.List;
import org.springframework.data.domain.Pageable;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 10, 2019
 * Jirra ACC-961
 */
public class TransactionPage extends AccountingPage{

    private Double totalIn;
    private Double totalOut;

    public TransactionPage(
            List<Object> content,
            Pageable pageable,
            long total,
            double sum,
            double totalIn,
            double totalOut) {
        super(content, pageable, total, sum);
        this.totalIn = totalIn;
        this.totalOut = totalOut;
    }
    
    public TransactionPage(
        List<Object> content,
        Pageable pageable,
        long total,
        double sum,
        double totalIn,
        double totalOut,
        double totalVat) {
        super(content, pageable, total, sum, totalVat);
        this.totalIn = totalIn;
        this.totalOut = totalOut;
    }
    

    public Double getTotalIn() {
        return totalIn;
    }

    public void setTotalIn(Double totalIn) {
        this.totalIn = totalIn;
    }

    public Double getTotalOut() {
        return totalOut;
    }

    public void setTotalOut(Double totalOut) {
        this.totalOut = totalOut;
    }
    
}
