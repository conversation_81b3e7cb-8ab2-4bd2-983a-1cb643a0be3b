package com.magnamedia.extra;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.magnamedia.module.type.ContractType;
import java.util.Date;
import org.joda.time.DateTime;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jan 13, 2019
 */
public class AdjustedEndDate {

    private String clientName;
    private DateTime adjustedEndDate;
    private DateTime initialAdjustedEndDate;
    private DateTime paidEndDate;
    private Integer accruedVacationDays;
    private Integer ungivenVacationDays;
    private Double totalSum;
    private Double liveInMonthlyFee;
    private Double liveOutMonthlyFee;
    private Double sumExtensions;
    private Double currentlyMonthlyFee;
    private Double liveInMonths;
    private Double liveInDays;
    private Double liveOutMonths;
    private Double liveOutDays;
    private Date startOfContract;
    private ContractType contractType;
    private Double ethiopianLiveInMonths;;
    private Double ethiopianLiveInDays;
    private Double kenyanLiveInMonths;;
    private Double kenyanLiveInDays;

    public AdjustedEndDate() {

    }

    public AdjustedEndDate(String clientName,
            DateTime adjustedEndDate,
            DateTime initialAdjustedEndDate,
            DateTime paidEndDate,
            Integer accruedVacationDays,
            Integer ungivenVacationDays,
            Double totalSum,
            Double liveInMonthlyFee,
            Double liveOutMonthlyFee,
            Double sumExtensions,
            Double currentlyMonthlyFee,
            Double liveInMonths,
            Double liveInDays,
            Double liveOutMonths,
            Double liveOutDays,
            Date startOfContract,
            ContractType contractType
            
    ) {
        this.clientName = clientName;
        this.adjustedEndDate = adjustedEndDate;
        this.initialAdjustedEndDate = initialAdjustedEndDate;
        this.paidEndDate = paidEndDate;
        this.accruedVacationDays = accruedVacationDays;
        this.ungivenVacationDays = ungivenVacationDays;
        this.totalSum = totalSum;
        this.liveInMonthlyFee = liveInMonthlyFee;
        this.liveOutMonthlyFee = liveOutMonthlyFee;
        this.sumExtensions = sumExtensions;
        this.currentlyMonthlyFee = currentlyMonthlyFee;
        this.liveInMonths = liveInMonths;
        this.liveInDays = liveInDays;
        this.liveOutMonths = liveOutMonths;
        this.liveOutDays = liveOutDays;

        this.startOfContract = startOfContract;

        this.contractType = contractType;

    }

    public AdjustedEndDate(String clientName, DateTime adjustedEndDate, DateTime initialAdjustedEndDate, DateTime paidEndDate, Integer accruedVacationDays, Integer ungivenVacationDays, Double totalSum, Double liveInMonthlyFee, Double liveOutMonthlyFee, Double sumExtensions, Double currentlyMonthlyFee, Double liveInMonths, Double liveInDays, Double liveOutMonths, Double liveOutDays, Date startOfContract, ContractType contractType, Double ethiopianLiveInMonths, Double ethiopianLiveInDays,Double kenyanLiveInMonths,Double kenyanLiveInDays) {
        this.clientName = clientName;
        this.adjustedEndDate = adjustedEndDate;
        this.initialAdjustedEndDate = initialAdjustedEndDate;
        this.paidEndDate = paidEndDate;
        this.accruedVacationDays = accruedVacationDays;
        this.ungivenVacationDays = ungivenVacationDays;
        this.totalSum = totalSum;
        this.liveInMonthlyFee = liveInMonthlyFee;
        this.liveOutMonthlyFee = liveOutMonthlyFee;
        this.sumExtensions = sumExtensions;
        this.currentlyMonthlyFee = currentlyMonthlyFee;
        this.liveInMonths = liveInMonths;
        this.liveInDays = liveInDays;
        this.liveOutMonths = liveOutMonths;
        this.liveOutDays = liveOutDays;
        this.startOfContract = startOfContract;
        this.contractType = contractType;
        this.ethiopianLiveInMonths = ethiopianLiveInMonths;
        this.ethiopianLiveInDays = ethiopianLiveInDays;
        this.kenyanLiveInDays=kenyanLiveInDays;
        this.kenyanLiveInMonths=kenyanLiveInMonths;
    }
    
    public AdjustedEndDate(String clientName, DateTime adjustedEndDate, DateTime initialAdjustedEndDate, DateTime paidEndDate, Integer accruedVacationDays, Integer ungivenVacationDays, Double totalSum, Double liveInMonthlyFee, Double liveOutMonthlyFee, Double sumExtensions, Double currentlyMonthlyFee, Double liveInMonths, Double liveInDays, Double liveOutMonths, Double liveOutDays, Date startOfContract, ContractType contractType, Double ethiopianLiveInMonths, Double ethiopianLiveInDays) {
        this.clientName = clientName;
        this.adjustedEndDate = adjustedEndDate;
        this.initialAdjustedEndDate = initialAdjustedEndDate;
        this.paidEndDate = paidEndDate;
        this.accruedVacationDays = accruedVacationDays;
        this.ungivenVacationDays = ungivenVacationDays;
        this.totalSum = totalSum;
        this.liveInMonthlyFee = liveInMonthlyFee;
        this.liveOutMonthlyFee = liveOutMonthlyFee;
        this.sumExtensions = sumExtensions;
        this.currentlyMonthlyFee = currentlyMonthlyFee;
        this.liveInMonths = liveInMonths;
        this.liveInDays = liveInDays;
        this.liveOutMonths = liveOutMonths;
        this.liveOutDays = liveOutDays;
        this.startOfContract = startOfContract;
        this.contractType = contractType;
        this.ethiopianLiveInMonths = ethiopianLiveInMonths;
        this.ethiopianLiveInDays = ethiopianLiveInDays;
        
    }


//    public AdjustedEndDate(DateTime adjustedEndDate,
//            DateTime initialAdjustedEndDate,
//            DateTime paidEndDate,
//            Integer accruedVacationDays) {
//        this.adjustedEndDate = adjustedEndDate;
//        this.initialAdjustedEndDate = initialAdjustedEndDate;
//        this.paidEndDate = paidEndDate;
//        this.accruedVacationDays = accruedVacationDays;
//    }
    @JsonSerialize(using = ToStringSerializer.class)
    public DateTime getAdjustedEndDate() {
        return adjustedEndDate;
    }

    public void setAdjustedEndDate(DateTime adjustedEndDate) {
        this.adjustedEndDate = adjustedEndDate;
    }

    @JsonSerialize(using = ToStringSerializer.class)

    public DateTime getInitialAdjustedEndDate() {
        return initialAdjustedEndDate;
    }

    public void setInitialAdjustedEndDate(DateTime initialAdjustedEndDate) {
        this.initialAdjustedEndDate = initialAdjustedEndDate;
    }

    @JsonSerialize(using = ToStringSerializer.class)
    public DateTime getPaidEndDate() {
        return paidEndDate;
    }

    public void setPaidEndDate(DateTime paidEndDate) {
        this.paidEndDate = paidEndDate;
    }

    public Integer getAccruedVacationDays() {
        return accruedVacationDays;
    }

    public void setAccruedVacationDays(Integer accruedVacationDays) {
        this.accruedVacationDays = accruedVacationDays;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Integer getUngivenVacationDays() {
        return ungivenVacationDays;
    }

    public void setUngivenVacationDays(Integer ungivenVacationDays) {
        this.ungivenVacationDays = ungivenVacationDays;
    }

    public Double getTotalSum() {
        return totalSum;
    }

    public void setTotalSum(Double totalSum) {
        this.totalSum = totalSum;
    }

    public Double getLiveInMonthlyFee() {
        return liveInMonthlyFee;
    }

    public void setLiveInMonthlyFee(Double liveInMonthlyFee) {
        this.liveInMonthlyFee = liveInMonthlyFee;
    }

    public Double getLiveOutMonthlyFee() {
        return liveOutMonthlyFee;
    }

    public void setLiveOutMonthlyFee(Double liveOutMonthlyFee) {
        this.liveOutMonthlyFee = liveOutMonthlyFee;
    }

    public Double getSumExtensions() {
        return sumExtensions;
    }

    public void setSumExtensions(Double sumExtensions) {
        this.sumExtensions = sumExtensions;
    }

    public Double getCurrentlyMonthlyFee() {
        return currentlyMonthlyFee;
    }

    public void setCurrentlyMonthlyFee(Double currentlyMonthlyFee) {
        this.currentlyMonthlyFee = currentlyMonthlyFee;
    }

    public Double getLiveInMonths() {
        return liveInMonths;
    }

    public void setLiveInMonths(Double liveInMonths) {
        this.liveInMonths = liveInMonths;
    }

    public Double getLiveInDays() {
        return liveInDays;
    }

    public void setLiveInDays(Double liveInDays) {
        this.liveInDays = liveInDays;
    }

    public Double getLiveOutMonths() {
        return liveOutMonths;
    }

    public void setLiveOutMonths(Double liveOutMonths) {
        this.liveOutMonths = liveOutMonths;
    }

    public Double getLiveOutDays() {
        return liveOutDays;
    }

    public void setLiveOutDays(Double liveOutDays) {
        this.liveOutDays = liveOutDays;
    }

    public Date getStartOfContract() {
        return startOfContract;
    }

    public void setStartOfContract(Date startOfContract) {
        this.startOfContract = startOfContract;
    }

    public ContractType getContractType() {
        return contractType;
    }

    public void setContractType(ContractType contractType) {
        this.contractType = contractType;
    }

    public Double getEthiopianLiveInMonths() {
        return ethiopianLiveInMonths;
    }

    public void setEthiopianLiveInMonths(Double ethiopianLiveInMonths) {
        this.ethiopianLiveInMonths = ethiopianLiveInMonths;
    }

    public Double getEthiopianLiveInDays() {
        return ethiopianLiveInDays;
    }

    public void setEthiopianLiveInDays(Double ethiopianLiveInDays) {
        this.ethiopianLiveInDays = ethiopianLiveInDays;
    }

    public Double getKenyanLiveInMonths() {
        return kenyanLiveInMonths;
    }

    public void setKenyanLiveInMonths(Double kenyanLiveInMonths) {
        this.kenyanLiveInMonths = kenyanLiveInMonths;
    }

    public Double getKenyanLiveInDays() {
        return kenyanLiveInDays;
    }

    public void setKenyanLiveInDays(Double kenyanLiveInDays) {
        this.kenyanLiveInDays = kenyanLiveInDays;
    }




}

