package com.magnamedia.extra;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 17, 2020
 */

public class FileUtils {

    public static byte[] getByteArray(Attachment attachment) {
        InputStream in = null;
        
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;

            in = Storage.getStream(attachment);

            // read bytes from the input stream and store them in buffer
            while ((len = in.read(buffer)) != -1) {
                // write bytes from the buffer into output stream
                os.write(buffer, 0, len);
            }
            return os.toByteArray();
        } catch (Exception e) {
            return null;
        } finally {
            StreamsUtil.closeStream(in);
        }
    }
}
