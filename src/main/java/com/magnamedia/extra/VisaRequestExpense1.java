/*
package com.magnamedia.extra;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.VisaRequestExpenseController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import com.magnamedia.entity.serializer.IdOnlySerializer;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.visa.ExpensePurpose;

import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

*/
/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 22, 2018
 *         Jirra ACC-352
 *//*

public class VisaRequestExpense1 {

    private Long visaRequestExpenseID;
    private String visaExpenseType;
    @JsonIgnore
    private VisaExpense visaExpense;
    private Date creationDate;
    private String name;
    private EmployeeType employeeType;
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private Housemaid housemaid;
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private OfficeStaff officeStaff;
    private PaymentType paymentType;
    private Boolean newEmployee = false;
    private ExpensePurpose purpose;
    private ExpenseStatus status;
    private Double amount;
    @JsonSerialize(using = IdOnlySerializer.class)
    private Transaction transaction;
    private Boolean maidVisaAEContract;
    private String contractType;
    private String description;
    private Bucket fromBucket;
    private Expense expense;
    private String referenceNumber;
    private Double charge;
    private Double vatCharge;
    private String equation;

    private static final Logger logger = Logger.getLogger(VisaRequestExpense1.class.getName());

    public VisaRequestExpense1() {
    }

    public VisaRequestExpense1(Object[] records, PicklistItem maidVisa) {
        this.visaRequestExpenseID = (records[0] != null) ? ((BigInteger) records[0]).longValue() : null;
        if (this.visaRequestExpenseID != null) {
            this.visaExpenseType = (records[1] != null) ? (String) records[1] : null;
            this.visaExpense = this.getVisaExpense();

            logger.log(Level.SEVERE, "visaRequestExpenseID: " + this.visaRequestExpenseID);
            logger.log(Level.SEVERE, "visaExpenseType: " + this.visaExpenseType);

            this.setVisaRequestExpense(this.visaExpense, maidVisa);
        }
    }

    public VisaRequestExpense1(VisaExpense expense, PicklistItem maidVisa) {
        logger.log(Level.SEVERE, "VisaExpense id: " + ((expense != null) ? expense.getId() : ""));
        logger.log(Level.SEVERE, "maidVisa code" + ((maidVisa != null) ? maidVisa.getCode() : ""));

        if (expense != null) {
            this.visaExpense = expense;
            this.visaExpenseType = expense.getVisaExpenseType();
            logger.log(Level.SEVERE, "Entity type" + visaExpenseType);
            this.setVisaRequestExpense(this.visaExpense, maidVisa);
        }
    }

    public void setVisaRequestExpense(VisaExpense expense, PicklistItem maidVisa) {
        this.creationDate = this.visaExpense.getCreationDate();
        this.name = this.visaExpense.getName();
        this.referenceNumber = this.visaExpense.getReferenceNumber();
        if (this.visaExpense.getRequest() != null) {
            logger.log(Level.SEVERE, "inner getRequest: ");
            if (this.visaExpense.getRequest().getOfficeStaff() != null) {
                this.officeStaff = this.visaExpense.getRequest().getOfficeStaff();
                this.employeeType = EmployeeType.Officestaff;
            } else if (this.visaExpense.getRequest().getHousemaid() != null) {
                this.housemaid = this.visaExpense.getRequest().getHousemaid();
                this.employeeType = EmployeeType.Housemaid;
            }

            this.newEmployee = this.visaExpense.getRequest().getNewEmployee() != null ?
                    this.visaExpense.getRequest().getNewEmployee() : false;

            logger.log(Level.SEVERE, "employeeType: " + employeeType);
            logger.log(Level.SEVERE, "newEmployee: " + newEmployee);
        }

        this.paymentType = this.visaExpense.getPaymentType();
        this.purpose = this.visaExpense.getPurpose();
        this.status = this.visaExpense.getStatus();
        this.amount = this.visaExpense.getAmount();
        this.vatCharge = this.visaExpense.getVatCharge();
        this.charge = this.visaExpense.getCharge();
        this.equation = this.visaExpense.getEquation();
        this.transaction = this.visaExpense.getTransaction();
        this.contractType = "";

        if (housemaid != null) {
            PicklistItem contractProspectType = null;

            List<Contract> contracts = Setup.getRepository(ContractRepository.class)
                    .findByHousemaidAndStatus(housemaid, ContractStatus.ACTIVE);
            if (contracts.size() > 0) {
                contractProspectType = contracts.get(0).getContractProspectType();
            } else {
                Contract lastContract = Setup.getRepository(ContractRepository.class)
                        .findFirstOneByHousemaidOrderByCreationDateDesc(housemaid);
                if (lastContract != null) {
                    contractProspectType = lastContract.getContractProspectType();
                }
            }

            if (contractProspectType == null) {
                this.maidVisaAEContract = false;
                this.contractType = "";
            } else {
                if (maidVisa != null && contractProspectType.getId().equals(maidVisa.getId())) {
                    this.maidVisaAEContract = true;
                    this.contractType = "Maid.Visa";
                } else {
                    this.maidVisaAEContract = false;
                    if (contractProspectType.getCode() != null && contractProspectType.getCode()
                            .equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)))
                        this.contractType = "Maid.cc";
                    else
                        this.contractType = contractProspectType.getName();
                    
                }
            }
        }

        if (this.visaExpense.getStatus() == ExpenseStatus.Added
                && this.visaExpense.getTransaction() != null) {
            this.description = this.visaExpense.getTransaction().getDescription();
            this.fromBucket = this.visaExpense.getTransaction().getFromBucket();
            this.expense = this.visaExpense.getTransaction().getExpense();
        } else {
            this.description = (this.housemaid != null ? this.housemaid.getName() :
                    (this.officeStaff != null ? this.officeStaff.getName() : ""))
                    + " / " + (this.newEmployee ? "New " : "Current ")
                    + (this.employeeType == null ? "" : this.employeeType.toString() + " / ")
                    + (this.purpose != null ? this.purpose.getLabel() : "");
            this.fromBucket = this.defaultFromBucket();
            this.expense = this.defaultExpense();

            logger.log(Level.SEVERE, "newEmployee: " + newEmployee);
            logger.log(Level.SEVERE, "description: " + description);
            logger.log(Level.SEVERE, "fromBucket: " + ((fromBucket != null) ? fromBucket.getCode() : ""));
            logger.log(Level.SEVERE, "fromBucket: " + ((this.expense != null) ? this.expense.getLabel() : ""));
        }

    }

    public Long getVisaRequestExpenseID() {
        return visaRequestExpenseID;
    }

    public void setVisaRequestExpenseID(Long visaRequestExpenseID) {
        this.visaRequestExpenseID = visaRequestExpenseID;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public EmployeeType getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(EmployeeType employeeType) {
        this.employeeType = employeeType;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        this.officeStaff = officeStaff;
    }

    public PaymentType getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentType paymentType) {
        this.paymentType = paymentType;
    }

    public Boolean getNewEmployee() {
        return newEmployee;
    }

    public void setNewEmployee(Boolean newEmployee) {
        this.newEmployee = newEmployee;
    }

    public ExpensePurpose getPurpose() {
        return purpose;
    }

    public void setPurpose(ExpensePurpose purpose) {
        this.purpose = purpose;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ExpenseStatus getStatus() {
        return status;
    }

    public void setStatus(ExpenseStatus status) {
        this.status = status;
    }

    public String getAmount() {
        DecimalFormat decimalFormat = new DecimalFormat("############.##");
        return decimalFormat.format(amount);
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getVisaExpenseType() {
        return visaExpenseType;
    }

    public void setVisaExpenseType(String visaExpenseType) {
        this.visaExpenseType = visaExpenseType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Bucket getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(Bucket fromBucket) {
        this.fromBucket = fromBucket;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public Boolean getMaidVisaAEContract() {
        return maidVisaAEContract;
    }

    public void setMaidVisaAEContract(Boolean maidVisaAEContract) {
        this.maidVisaAEContract = maidVisaAEContract;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public String getReferenceNumberCSV() {
        return referenceNumber != null ? "'" + referenceNumber : "";
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public VisaExpense getVisaExpense() {
        if (this.visaExpenseType.equalsIgnoreCase("CancelRequestExpense")) {
            CancelVisaRequestExpenseRepository repository =
                    Setup.getRepository(CancelVisaRequestExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("NewRequestExpense")) {
            NewVisaRequestExpenseRepository repository =
                    Setup.getRepository(NewVisaRequestExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("RepeatEIDRequestExpense")) {
            RepeatEIDExpenseRepository repository =
                    Setup.getRepository(RepeatEIDExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("RenewRequestExpense")) {
            RenewVisaRequestExpenseRepository repository =
                    Setup.getRepository(RenewVisaRequestExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("ContractModificationExpense")) {
            ContractModificationExpenseRepository repository =
                    Setup.getRepository(ContractModificationExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else if (this.visaExpenseType.equalsIgnoreCase("ModifyVisaRequestExpense")) {
            ModifyVisaRequestExpenseRepository repository =
                    Setup.getRepository(ModifyVisaRequestExpenseRepository.class);
            return repository.findOne(this.visaRequestExpenseID);
        } else
            return null;
    }

    public Expense defaultExpense() {

        if (this.getPaymentType() == null ||
                this.getEmployeeType() == null ||
                (!this.getEmployeeType().equals(EmployeeType.Housemaid) &&
                        !this.getEmployeeType().equals(EmployeeType.Officestaff)))
            return this.expense;


        String expenseCode = this.getEmployeeType().equals(EmployeeType.Housemaid) ?
                defaultExpenseForHouseMaid() : defaultExpenseForOfficeStaff();

        if (expenseCode != null) {
            this.setExpense(Setup.getRepository(ExpenseRepository.class)
                .findByCodeAndDeletedFalse(expenseCode));
        }


        return this.expense;
    }

    public String defaultExpenseForOfficeStaff() {

        String expenseCode = null;
        switch (this.getPaymentType()) {
            case Noqoodi:
                if (this.purpose.equals(ExpensePurpose.REFUND_FOR_ENTRY_VISA)) {
                    if (this.newEmployee)
                        expenseCode = "VE 12";
                }
                break;
            case Credit_Card:
                switch (this.purpose) {
                    case UNPAID_LEAVE:
                        if (this.employeeType.equals(EmployeeType.Officestaff))
                            expenseCode = "VE 12";
                        break;
                    case GOOD_CONDUCT_CERTIFICATE:
                    case CHALLENGING_OVERSTAY_FINES:
                        if (this.newEmployee)
                            expenseCode = "VE 12" ;
                        break;
                }
                break;
        }

        return expenseCode;
    }

    public String defaultExpenseForHouseMaid() {

        String expenseCode = null;
        switch (this.getPaymentType()) {
            case Edirhams:
                switch (this.purpose) {
                    case PAY_LABOR_CARD_FEE:
                        expenseCode = this.maidVisaAEContract ? "MV 09" : "FT 16";
                        break;
                    case SUBMIT_RENEW_LABOR_CARD_APPICATION:
                        expenseCode = this.maidVisaAEContract ? "MV 10" : "FT 19";
                        break;
                }
                break;
            case Noqoodi:
                switch (this.purpose) {
                    case MEDICAL:
                        expenseCode = this.maidVisaAEContract ? "MV 34" : "FT 188";
                        break;
                    case MEDICAL_RENEW:
                        expenseCode = this.maidVisaAEContract ? "MV 35" : "FT 189";
                        break;
                    case CHANGE_OF_STATUS:
                    case ENTRY_VSIA:
                    case APPLY_FOR_RVISA:
                    case REFUND_FOR_ENTRY_VISA:
                        if (this.newEmployee)
                            expenseCode = this.maidVisaAEContract ? "MV 11" : "FT 15";
                        break;
                    case GDRFA_ABSCONDING:
                    case RESIDENCE_CANCELLATION:
                    case TRAVEL_REPORT:
                        if (!this.newEmployee)
                            expenseCode = this.maidVisaAEContract ? "MV 12" : "FT 18";
                        break;
                    case UPDATE_INFORMATION_IN_IMMIGRATION:
                        if (this.newEmployee)
                            expenseCode = this.maidVisaAEContract ? "MV 11" : "FT 15";
                        else expenseCode = this.maidVisaAEContract ? "MV 12" : "FT 18";
                        break;
                }
                break;
            case CBD:
                if (this.purpose.toString().toLowerCase().contains("eid")) {
                    if (this.newEmployee)
                        expenseCode = this.maidVisaAEContract ? "MV 13" : "FT 186";
                    else expenseCode = this.maidVisaAEContract ? "MV 21" : "FT 187";

                }
                break;
            case Credit_Card:
                if (this.purpose.toString().toLowerCase().contains("eid")) {
                    if (this.newEmployee)
                        expenseCode = this.maidVisaAEContract ? "MV 13" : "FT 186";
                    else expenseCode = this.maidVisaAEContract ? "MV 21" : "FT 187";
                    break;
                }

                switch (this.purpose) {
                    case PAY_LABOR_CARD_FEE:
                        expenseCode = this.maidVisaAEContract ? "MV 09" : "FT 16";
                        break;
                    case SUBMIT_RENEW_LABOR_CARD_APPICATION:
                        expenseCode = this.maidVisaAEContract ? "MV 10" : "FT 19";
                        break;
                    case CREATE_OFFER_LETTER:
                    case WORK_PERMIT:
                    case GOOD_CONDUCT_CERTIFICATE:
                        if (this.newEmployee)
                            expenseCode = this.maidVisaAEContract ? "MV 09" : "FT 16";
                        break;
                    case MOHRE_INSURANCE:
                    case UPDATE_PHONE_NUMBER_IN_ICA:
                    case MODIFY_CONTRACT:
                    case UNPAID_LEAVE:
                        if (this.newEmployee)
                            expenseCode = this.maidVisaAEContract ? "MV 09" : "FT 16";
                        else expenseCode = this.maidVisaAEContract ? "MV 10" : "FT 19";
                        break;
                    case CHALLENGING_OVERSTAY_FINES:
                        if (this.newEmployee)
                            expenseCode = this.maidVisaAEContract ? "MV 11" : "FT 15";
                        break;
                }
                break;
        }

        return expenseCode;
    }

    public Bucket defaultFromBucket() {

        if (this.getPaymentType() == null ||
                this.getEmployeeType() == null ||
                (!this.getEmployeeType().equals(EmployeeType.Housemaid) &&
                        !this.getEmployeeType().equals(EmployeeType.Officestaff)))
            return this.fromBucket;

        String bucketCode = null;
        switch (this.getPaymentType()) {
            case Credit_Card:
                if (visaExpense.getDefaultBucketCode() != null) {
                    bucketCode = visaExpense.getDefaultBucketCode();
                } else {
                    String url = "/visa/rpa-config/getActiveCardInfo";
                    logger.log(Level.SEVERE, "MMM URL: " + url);
                    LinkedHashMap responseEntity = Setup.getApplicationContext().getBean(InterModuleConnector.class)
                            .get(url, LinkedHashMap.class);
                    bucketCode = (String) responseEntity.get("bucketCode");
                    visaExpense.setDefaultBucketCode(bucketCode);
                    Setup.getApplicationContext().getBean(VisaRequestExpenseController.class)
                            .saveVisaExpenseByType(visaExpenseType, visaExpense);
                }

                break;
            case Noqoodi:
                bucketCode = Setup.getParameter(Setup.getCurrentModule(),
                        this.getPaymentType().toString().toLowerCase() + "_visa_expense_transaction_from_bucket");
                break;
            case CBD:
            case Edirhams:
                if (this.getEmployeeType().equals(EmployeeType.Housemaid)) {
                    bucketCode = Setup.getParameter(Setup.getCurrentModule(),
                            this.getPaymentType().toString().toLowerCase() + "_visa_expense_transaction_from_bucket");
                }
                break;
        }

        if (bucketCode != null && !bucketCode.isEmpty())
            this.setFromBucket(Setup.getRepository(BucketRepository.class)
                    .findByCode(bucketCode));

        return this.fromBucket;
    }

    public Double getCharge() {
        return charge;
    }

    public void setCharge(Double charge) {
        this.charge = charge;
    }

    public Double getVatCharge() {
        return vatCharge;
    }

    public void setVatCharge(Double vatCharge) {
        this.vatCharge = vatCharge;
    }

    public String getEquation() {
        return equation;
    }

    public void setEquation(String equation) {
        this.equation = equation;
    }
}
*/
