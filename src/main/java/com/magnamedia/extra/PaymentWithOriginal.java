package com.magnamedia.extra;

import com.magnamedia.module.type.ExpensePaymentMethod;

/**
 * Created by Mamon.masod on 3/23/2021.
 */
public interface PaymentWithOriginal {

    Long getTransactionId();

    Long getExpenseToPostId();

    Long getFromBucketId();
    
    Long getCurrencyId();
    
    Boolean getTaxable();

    Double getLoanAmount();
    
    String getCurrencyName();
    
    String getFromBucketName();

    String getExpenseToPostName();

    ExpensePaymentMethod getPaymentMethod();

    Double getAmount();

    Double getVatAmount();

    String getDescription();
}
