package com.magnamedia.extra;


public class DirectDebitGenerationPlaneMigrationReportCSV {
    private int number;
    private String contractId;
    private String planId;
    private String ddSendDate;
    private String ddExpiryDate;
    private String ddGenerationDate;
    private String newDdSendDate;
    private String newDdExpiryDate;
    private String newDdGenerationDate;
    private String notes;

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getDdSendDate() {
        return ddSendDate;
    }

    public void setDdSendDate(String ddSendDate) {
        this.ddSendDate = ddSendDate;
    }

    public String getDdExpiryDate() {
        return ddExpiryDate;
    }

    public void setDdExpiryDate(String ddExpiryDate) {
        this.ddExpiryDate = ddExpiryDate;
    }

    public String getDdGenerationDate() {
        return ddGenerationDate;
    }

    public void setDdGenerationDate(String ddGenerationDate) {
        this.ddGenerationDate = ddGenerationDate;
    }

    public String getNewDdSendDate() {
        return newDdSendDate;
    }

    public void setNewDdSendDate(String newDdSendDate) {
        this.newDdSendDate = newDdSendDate;
    }

    public String getNewDdExpiryDate() {
        return newDdExpiryDate;
    }

    public void setNewDdExpiryDate(String newDdExpiryDate) {
        this.newDdExpiryDate = newDdExpiryDate;
    }

    public String getNewDdGenerationDate() {
        return newDdGenerationDate;
    }

    public void setNewDdGenerationDate(String newDdGenerationDate) {
        this.newDdGenerationDate = newDdGenerationDate;
    }

    public String getNotes() { return notes; }

    public void setNotes(String notes) { this.notes = notes; }
}
