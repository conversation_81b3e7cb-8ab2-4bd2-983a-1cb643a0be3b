package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 8, 2019
 * Jirra ACC-874
 */
public interface InsuranceNoteCSV {

    Long getMaidId();
    String getMaidName();
    Date getEndorsementDate();
    Date getTerminationDate();
    Integer getNumberOfDays();
    Double getPremium();
    Date getInsuranceStartDate();
    Date getInsuranceEndDate();
    String getWorkerType();
    String getBasmaCharge();
    @Value("#{(\"'\"+target.getPassportNumber())}")
    String getPassportNumber();
    String getEmiratesId();
    Date getJoiningDate(); //ACC-4655
    Double getTotalAmount();

}
