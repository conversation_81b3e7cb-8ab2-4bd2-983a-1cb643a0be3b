package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.serializer.PaymentJsonSerializer;
import com.magnamedia.module.type.PaymentMatchingRecordStatus;
import com.magnamedia.module.type.PaymentStatus;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Aug 17, 2020
 *         Jirra ACC-2330
 */

public interface PaymentMatchingRecordProjection {
    Long getId();

    String getPaymentIdStr();

    String getStatus();

    String getReasonOfBouncedCheque();

    PaymentStatus getPrevStatus();

    Boolean getConfirmed();

    @JsonSerialize(using = PaymentJsonSerializer.class)
    Payment getPayment();

    PaymentMatchingRecordStatus getRecordStatus();

    String getTransferringFailureReason();
}
