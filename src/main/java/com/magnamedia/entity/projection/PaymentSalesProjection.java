package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 5, 2020
 *         Jirra ACC-1435
 */

public interface PaymentSalesProjection {

    Long getId();

    List<Attachment> getAttachments();

    Double getAmountOfPayment();

    @JsonSerialize(using = IdLabelSerializer.class)
    Contract getContract();

    Date getDateOfPayment();

    PaymentStatus getStatus();

    PaymentMethod getMethodOfPayment();

    @JsonSerialize(using = IdLabelSerializer.class)
    PicklistItem getTypeOfPayment();

    Boolean getIgnoreVAT();

    Boolean getIsInitial();

    Boolean getReplaced();

    String getUuid();

    long getVersion();

    java.util.Date getLastModificationDate();

    java.util.Date getCreationDate();

    @Value("#{target.getCreator() != null ? target.getCreator().getLoginName() : ''}")
    String getCreator();

    @Value("#{target.getCreatorModule() != null ? target.getCreatorModule().getName() : ''}")
    String getCreatorModule();

    @Value("#{target.getLastModifier() != null ? target.getLastModifier().getLoginName() : ''}")
    String getLastModifier();
}
