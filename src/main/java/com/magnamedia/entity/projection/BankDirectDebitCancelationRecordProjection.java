package com.magnamedia.entity.projection;

import java.util.Map;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
public interface BankDirectDebitCancelationRecordProjection {

    public Long getId();

    @Value("#{target.getDirectDebitFile() != null ? "
            + "{id: target.getDirectDebitFile().getId(), "
            //+ "attachments: target.getDirectDebitFile().getAttachments(), "
            + "type: target.getDirectDebitFile().getDdFrequency().getValue(), "
            + "presentmentDate: target.getDirectDebitFile().getPresentmentDate(), "
            + "startDate: target.getDirectDebitFile().getStartDate(), "
            + "expiryDate: target.getDirectDebitFile().getExpiryDate(), "
            + "amount: target.getDirectDebitFile().getAmount(), "
            + "accountName: target.getDirectDebitFile().getAccountName(), "
            + "applicationId: target.getDirectDebitFile().getApplicationId(), "
            + "status: target.getDirectDebitFile().getDdStatus(), "
            + "contractPaymentTerm: (target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm() != null ? "
            + "{id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getId(), "
            //+ "attachments: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getAttachments(), "
            + "contract: (target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract() != null ? "
            + "{id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId(), "
            + "client: (target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient() != null ? "
            + " {id: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient().getId(), "
            + "name: target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()}"
            + ": null)} "
            + ": null), "
            + "bankName : target.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getBankName()} "
            + ": null)}"
            + ": null}")
    public Map<?, ?> getDirectDebitFile();

    public Integer getRowIndex();

    public String getDdaRefNo();

    public String getCancelReason();

    public String getErrorMessage();

    public boolean isConfirmed();

    public boolean getProcessing();
}
