package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Template;
import com.magnamedia.entity.serializer.TemplateSerilizer;
import com.magnamedia.module.type.DDMessagingType;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 17-8-2020
 * Jirra ACC-2371
 */
public interface DDMessagingListProjection {
    
    public Long getId();

    DDMessagingType getEvent();

    String getMaidMessage();
    
    @JsonSerialize(using = TemplateSerilizer.class)
    Template getMaidTemplate();

    String getClientMessage();
    
    @JsonSerialize(using = TemplateSerilizer.class)
    Template getClientTemplate();

    String getSmsText();
    
    String getReminders();

    String getTrials();

    Boolean getActive();
}
