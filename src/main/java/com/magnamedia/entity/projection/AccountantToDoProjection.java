package com.magnamedia.entity.projection;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.CooQuestion;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;

public interface AccountantToDoProjection {

    Long getId();

    Date getCreationDate();

    String getTodoCategory();

    String getTaskName();

    String getLabel();

    @Value("#{target.getSendRequestForApprovalAction() !=null ? target.getSendRequestForApprovalAction().getId() : null}")
    String getSendRequestForApprovalAction();

    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getAmount() != null ? target.getAmount().toString(): '' )}")
    String getAmount();

    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getTotal() != null ? target.getTotal().toString(): '' )}")
    String getTotal();

    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getAmountAED() != null ? target.getAmountAED().toString(): '' )}")
    String getAmountAED();

    @Value("#{target.shouldAmountBeSecured() ? '****' : ((target.getTotalInAED() != null && target.getTotalInAED() != 0D) ? target.getTotalInAED().toString(): '' )}")
    String getTotalInAED();

    @Value("#{target.shouldAmountBeSecured() ? '****' : (target.getAmountInForeignCurrency() != null ? target.getAmountInForeignCurrency().toString(): '' )}")
    String getAmountInForeignCurrency();

    String getForeignCurrency();

    SalaryCurrency getCurrency();

    String getAccountName();

    String getIban();
    
    String getAccountNumber();
    
    String getMobileNumber();
    
    String getSwift();
    
    String getAddress();
    
    Boolean getInternational();
    
    @JsonSerialize(using = IdLabelSerializer.class)
    @JsonProperty("todoType")
    PicklistItem getItemType();

    String getBeneficiary();

    String getDueSince();

    String getRequester();

    @JsonSerialize(using = IdLabelSerializer.class)
    User getRequesterUser();

    String getApprovalFlow();

    @JsonSerialize(using = IdLabelSerializer.class)
    User getApprover();

    String getDescription();

    String getManagerUserName();

    PayrollAccountantTodoManagerAction getManagerAction();

    String getEntityType();

    List<CooQuestion> getCooQuestions();

    boolean isOneQuestionAnswered();

    boolean isNoneQuestionAnswered();

    boolean isAllQuestionsAnswered();

    // JIRA ACC-4654
    PicklistItem getContractProspectType();

    @Value("#{target.attachments != null ? target.attachments.?[tag != null && (tag.equals('proofOfTransfer') || tag.equals('client_refund_transfer_slip'))] : null }")
    List<Attachment> getAttachments();

    public Map<String, String> getBeneficiaryType();

    public Long getBeneficiaryTypeId();

    String getBankName();

    String getBankCountry();

    String getBankCity();
}
