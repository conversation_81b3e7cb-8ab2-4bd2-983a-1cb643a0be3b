package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.module.type.ClientSource;
import com.magnamedia.module.type.NeedOfMaidDuration;
import com.magnamedia.module.type.PortType;
import com.magnamedia.module.type.WhenToContact;
import org.springframework.beans.factory.annotation.Value;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 08, 2020
 *         Jirra ACC-1435
 */
public interface ClientSalesProjection {

    Long getId();

    List<Attachment> getAttachments();

    NeedOfMaidDuration getNeedOfMaidDuration();

    @JsonSerialize(using = IdLabelListSerializer.class)
    List<Contract> getActiveContracts();

    Boolean getSmsConfirmed();

    @Value("#{target.getSalesBlocked()}")
    Boolean getBlocked();

    Integer getKidsUnder12Years();

    Boolean getSmsMsgSent();

    PortType getPort();

    Boolean getWithBotProspects();

    String getAddedDate();

    String getAddedTime();

    Boolean getHasNoWhatsAppNumber();

    Timestamp getQuartetShareDateTime();

    Boolean getEngagedSameDay();

    List<InterviewVisit> getActiveInterviews();

    PicklistItem getSearchNotEqualInitialContact();

    List<BlockLog> getBlockLogs();

    String getName();

    String getMobileNumber();

    String getWhatsappNumber();

    String getSpouseMobileNumber();

    String getOtherMatchingTypes();

    @JsonSerialize(using = IdLabelSerializer.class)
    PicklistItem getInitialContact();

    String getSpouseName();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getStatus();

    Boolean getSent();

    String getToken();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getProspectType();

    Double getEagernessforOurService();

    Double getAgencyFeeConcern();

    Double getMonthlyConcern();

    String getInstructionstoOutboundEnchanters();

    String getNormalizedMobileNumber();

    String getNormalizedWhatsappNumber();

    String getNormalizedSpouseMobileNumber();

    Boolean getHasActiveContract();

    String getEid();

    Timestamp getTokenGenerationTime();

    Timestamp getFirstPageVisit();

    Long getPreSalesBotId();

    String getMaxPanelId();

    String getFullAddress();

    @JsonSerialize(using = IdLabelSerializer.class)
    PicklistItem getCity();

    @JsonSerialize(using = IdLabelSerializer.class)
    PicklistItem getTitle();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getNationality();

    String getInstructionsToTheMaid();

    String getSpouseWhatsappNumber();

    String getNormalizedSpouseWhatsappNumber();

    String getEmail();

    Timestamp getLastPageVisit();

    Integer getVidVisitsCount();

    WhenToContact getWhenToContact();

    String getNotes();

    String getDiscountCode();

    ClientSource getSource();

    String getUuid();

    long getVersion();

    Date getLastModificationDate();

    Date getCreationDate();

    @Value("#{target.getCreator() != null ? target.getCreator().getLoginName() : ''}")
    String getCreator();

    @Value("#{target.getCreatorModule() != null ? target.getCreatorModule().getName() : ''}")
    String getCreatorModule();

    @Value("#{target.getLastModifier() != null ? target.getLastModifier().getLoginName() : ''}")
    String getLastModifier();

    List<ClientAgePreference> getAgePreferences();

    List<ClientRemark> getRemarks();

    PromotionalEvent getPromotionalEvent();

    CorporateCompany getCorporateCompany();
}
