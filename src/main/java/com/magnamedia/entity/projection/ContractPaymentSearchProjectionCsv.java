package com.magnamedia.entity.projection;

import com.magnamedia.module.type.PaymentMethod;
import java.util.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR>
 * <<EMAIL>>
 * Created on Apr 3, 2019
 * Jirra ACC-494
 */
public interface ContractPaymentSearchProjectionCsv {
    
    @Value("#{target.getContractPaymentTerm() != null && "
            + "target.getContractPaymentTerm().getContract() != null && "
            + "target.getContractPaymentTerm().getContract().getClient() != null ? "
            + "target.getContractPaymentTerm().getContract().getClient().getName()"
            + ": ''}")
    String getClient();

    @Value("#{target.getContractPaymentTerm() != null && "
            + "target.getContractPaymentTerm().getContract() != null && "
            + "target.getContractPaymentTerm().getContract().getHousemaid() != null ? "
            + "target.getContractPaymentTerm().getContract().getHousemaid().getName()"
            + ": ''}")
    String getHousemaid();
    
    @Value("#{target.getContractPaymentTerm() != null && "
            + "target.getContractPaymentTerm().getContract() != null ? "
            + "target.getContractPaymentTerm().getContract().getId()"
            + ": null}")
    Long getContractId();
    
    @Value("#{target.getContractPaymentTerm() != null && "
            + "target.getContractPaymentTerm().getContract() != null && "
            + "target.getContractPaymentTerm().getContract().getContractType() != null ? "
            + "target.getContractPaymentTerm().getContract().getContractType() "
            + ": ''}")
    String getContractType();
    
    @Value("#{target.getContractPaymentTerm() != null && "
            + "target.getContractPaymentTerm().getContract() != null && "
            + "target.getContractPaymentTerm().getContract().getHousemaid() != null && "
            + "target.getContractPaymentTerm().getContract().getHousemaid().getNationality() != null ? "
            + "target.getContractPaymentTerm().getContract().getHousemaid().getNationality().getName()"
            + ": ''}")
    String getNationality();
    
    @Value("#{target.getDate()}")
    Date getPaymentDate();
    
    Date getCreationDate();
    Double getAmount();
    PaymentMethod getPaymentMethod();
    @Value("#{target.getPaymentType() != null ? target.getPaymentType().getName() : ''}")
    String getPaymentType();
    int getAttachmentsCount();
    //ACC-952
    Double getAdditionalDiscountAmount();
    @Value("#{target.getAdditionalDiscountAmount() != null && target.getAdditionalDiscountAmount() != 0 && target.getContractPaymentTerm() != null? "
            + "target.getContractPaymentTerm().getAdditionalDiscountNotes() "
            + ": ''}")
    String getAdditionalDiscountNotes();
    //ACC-1051
    @Value("#{(target.getContractPaymentTerm() != null) ? "
            + "target.getContractPaymentTerm().isIsActive() : ''}")
    Boolean getIsActive();

}
