//package com.magnamedia.entity.projection;
//
//import com.magnamedia.core.entity.Attachment;
//import com.magnamedia.entity.DirectDebitFile;
//import com.magnamedia.module.type.DirectDebitStatus;
//import com.magnamedia.module.type.DirectDebitType;
//import com.magnamedia.module.type.PaymentMethod;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import org.springframework.beans.factory.annotation.Value;
//
///**
// *
// * <AUTHOR>
// * <<EMAIL>>
// * Created on Mar 4, 2019
// * Jirra ACC-456
// */
//public interface DDSearchProjection {
//
//    Long getId();
//    @Value("#{target.getContractPaymentTerm() != null ? "
//                + "{id:target.getContractPaymentTerm().getId(), "
//                + "isActive:target.getContractPaymentTerm().isIsActive(), "
//                + "attachments:target.getContractPaymentTerm().getAttachments(), "
//                + "additionalDiscount:target.getContractPaymentTerm().getAdditionalDiscount(), "
//                + "additionalDiscountNotes:target.getContractPaymentTerm().getAdditionalDiscountNotes(), "
//                + "contract:(target.getContractPaymentTerm().getContract() != null ? "
//                    + "{id: target.getContractPaymentTerm().getContract().getId(), "
//                    + "contractType: target.getContractPaymentTerm().getContract().getContractType(), "
//                    + "client: (target.getContractPaymentTerm().getContract().getClient() != null ? "
//                        + " {id: target.getContractPaymentTerm().getContract().getClient().getId(), "
//                        + "name: target.getContractPaymentTerm().getContract().getClient().getName()}"
//                    + ": null), "
//                    + "housemaid: (target.getContractPaymentTerm().getContract().getHousemaid() != null ? "
//                        + " {id: target.getContractPaymentTerm().getContract().getHousemaid().getId(), "
//                        + "name: target.getContractPaymentTerm().getContract().getHousemaid().getName(),"
//                        + "nationality: (target.getContractPaymentTerm().getContract().getHousemaid().getNationality() != null ? "
//                                    + " {id: target.getContractPaymentTerm().getContract().getHousemaid().getNationality().getId(), "
//                                    + "name: target.getContractPaymentTerm().getContract().getHousemaid().getNationality().getName()}"
//                                + ": null)}"
//                    + ": null)} "
//                + ": null), "
//                + "bankName : target.getContractPaymentTerm().getBankName()} "
//            + ": null}")
//    Map<?, ?> getContractPaymentTerm();
//    String getDdaRefNo();
//    //Jirra ACC-1588
////    String getApplicationId();
//    Date getCreationDate();
//    Date getStartDate();
//    Date getExpiryDate();
//    DirectDebitStatus getStatus();
//    Boolean getUploaded();
//    Date getResultDate();
//    List<Attachment> getAttachments();
//    String getNotes();
//    String getRejectionReason();
//    //Jirra ACC-616
//    double getAmount();
//    //Jirra ACC-606
//    Double getAdditionalDiscount();
//    //ACC-952
//    @Value("#{target.getAdditionalDiscountNotes() != null && !target.getAdditionalDiscountNotes().isEmpty()? "
//            + "target.getAdditionalDiscountNotes() : "
//            + "(target.getAdditionalDiscount() != null && target.getAdditionalDiscount() != 0 && target.getContractPaymentTerm() != null ? target.getContractPaymentTerm().getAdditionalDiscountNotes(): null)}")
//    String getAdditionalDiscountNotes();
//    @Value("#{target.getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT') != null ? "
//            + "target.getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT') : "
//            + "(target.getAdditionalDiscount() != null && target.getAdditionalDiscount() != 0 && target.getContractPaymentTerm() != null ? target.getContractPaymentTerm().getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT'): null)}")
//    Attachment getAdditionalDiscountAttachment();
//
//    //Jirra ACC-786
//    List<DirectDebitFile> getDirectDebitFiles();
//    //Jirra ACC-786
//    PaymentMethod getPaymentMethod();
//    @Value("#{target.getPaymentType() != null ? "
//                + "{id:target.getPaymentType().getId(), "
//                + "name:target.getPaymentType().getName()} "
//                + ": null}")
//    Map<?, ?> getPaymentType();
//    int getPaymentsCount();
//
//    //Jirra ACC-786 ACC-1435
//    @Value("#{target.getType() != null ? target.getType().getValue() : ''}")
//    String getType();
//
//    //Jirra ACC-1340
//    Boolean getConfirmedBankInfo();
//    Boolean getNonCompletedInfo();
//    Boolean getNonFileCompleted();
//}
