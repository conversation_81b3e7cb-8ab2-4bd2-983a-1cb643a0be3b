package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.HousemaidSerilizer;
import com.magnamedia.module.type.*;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 08, 2020
 *         Jirra ACC-1435
 */
public interface ContractSalesProjection {

    Long getId();

    List<Attachment> getAttachments();

    @JsonSerialize(using = IdLabelSerializer.class)
    Client getClient();

    @JsonSerialize(using = HousemaidSerilizer.class)
    Housemaid getHousemaid();

    ContractStatus getStatus();

    @JsonSerialize(using = IdLabelSerializer.class)
    InterviewVisit getInterview();

    Date getStartOfContract();

    @Value("#{target.getEndOfContractCSV()}")
    String getEndOfContract();

    Date getLeaveOfficeDate();

    Date getArriveClientHomeDate();

    String getEmiratesId();

    Double getNumberOfPDCs();

    Double getRecruitmentFee();

    Double getLiveOutFee();

    Double getValueOfEachPDC();

    HousemaidArrival getHousemaidArrival();

    HousemaidLiveplace getLiving();

    @Value("#{target.getAdjustedEndDateCSV()}")
    String getAdjustedEndDate();

    Date getPaidEndDate();

    ContractType getContractType();

    Date getPassportExpirtyDate();

    Date getEmiratesIdFronSideExpirtyDate();

    Date getEmiratesIdBackSideExpirtyDate();

    Date getVisaExpirtyDate();

    Date getEjariExpirtyDate();

    Date getTenancyContractExpirtyDate();

    Date getMariageCertificateExpirtyDate();

    Date getCorporateCompanyWorkProofExpirtyDate();

    Double getAdditionalDiscount();

    Integer getProRatedDays();

    String getAdditionalDiscountNotes();

    @JsonSerialize(using = IdLabelSerializer.class)
    PicklistItem getHearedAboutUs();

    String getHearedAboutUsOther();

    Double getDefaultST();

    List<LiveInOutLog> getLiveInOutLogs();

    String getHousemaidNote();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getNationality();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getContractProspectType();

    String getGoogleSearchKeysOther();

    Double getFirstMonthPayment();

    @JsonSerialize(using = IdLabelSerializer.class)
    PicklistItem getWorkerNationality();

    String getWorkerName();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getWorkerType();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getWorkerCurrentSituation();

    Gender getWorkerGender();

    Double getAgencyFee();

    Double getMonthlyPayment();

    Double getWorkerSalary();

    String getWorkerPassportNumber();

    Double getOverstayFee();

    WorkerPrevVisaType getWorkerPrevVisaType();

    ContractFeesType getContractFeesType();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getWorkerInitialLocation();

    PickupType getPickupType();

    Boolean getClientWantTalkMaid();

    String getPickupGoogleMapKeywords();

    String getPickupNotes();

    Date getWorkerBirthDate();

    Date getWorkerPassportExpiryDate();

    String getWorkerCarRegNumber();

    String getWorkerFirstName();

    String getWorkerMiddleName();

    String getWorkerLastName();

    Integer getPaymentsDuration();

    Boolean getIsProRated();

    Boolean getWorkerInHerCountry();

    String getWorkerMobileInCountry();

    String getWorkerAddressInCountry();

    Date getWorkerArriveDubaiDate();

    @JsonSerialize(using = IdLabelSerializer.class)
    Contract getOldRenewedContract();

    Boolean getUrgentMedical();

    ContractPackageType getPackageType();

    @JsonSerialize(using = IdLabelSerializer.class)
    PaymentTermConfig getPaymentTermConfig();

    RVisaProcedureType getWorkerRVisaProcedureType();

    @Value("#{target.getScheduledDateOfTerminationCSV()}")
    String getScheduledDateOfTermination();

    Date getDateOfTermination();

    Boolean getClientPaidVat();

    String getDiscountCode();

    Boolean getIsScheduledForTermination();

    Boolean getProRatedPlusMonth();

    Boolean getWorkerHasPrevSponsor();

    Boolean getWorkerPrevSponsorCancelled();

    String getUuid();

    long getVersion();

    Date getLastModificationDate();

    Date getCreationDate();

    @Value("#{target.getCreator() != null ? target.getCreator().getLoginName() : ''}")
    String getCreator();

    @Value("#{target.getCreatorModule() != null ? target.getCreatorModule().getName() : ''}")
    String getCreatorModule();

    @Value("#{target.getLastModifier() != null ? target.getLastModifier().getLoginName() : ''}")
    String getLastModifier();

    boolean isPayingViaCreditCard();
}
