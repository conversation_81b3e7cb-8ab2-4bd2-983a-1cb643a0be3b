package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Oct 14, 2020
 *         Jirra ACC-2522
 */

public interface AccountBalanceTransactionProjection {

    Long getId();

    java.util.Date getPnlValueDate();

    @Value("#{(target.getFromBucket()!=null)?{id:target.getFromBucket().getId(), name:target.getFromBucket().getName(), code:target.getFromBucket().getCode()}:{id:\"\"}}")
    Map<?, ?> getFromBucket();

    @Value("#{(target.getRevenue()!=null)?{id:target.getRevenue().getId(), name:target.getRevenue().getName(), code:target.getRevenue().getCode()}:{id:\"\"}}")
    Map<?, ?> getRevenue();

    @Value("#{(target.getExpense()!=null)?{id:target.getExpense().getId(), name:target.getExpense().getNameLabel(), code:target.getExpense().getCodeLabel()}:{id:\"\"}}")
    Map<?, ?> getExpense();

    @Value("#{(target.getToBucket()!=null)?{id:target.getToBucket().getId(), name:target.getToBucket().getName(), code:target.getToBucket().getCode()}:{id:\"\"}}")
    Map<?, ?> getToBucket();

    String getDescription();

    Double getAmount();

    Date getDate();

    java.util.Date getCreationDate();
}
