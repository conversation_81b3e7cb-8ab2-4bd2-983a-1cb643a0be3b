package com.magnamedia.entity.projection;

/**
 * <AUTHOR>
 * @created 04/12/2024 - 10:42 AM
 * ACC-8444
 */
public interface DirectDebitGenerationPlanPendingPayingViaCcReportCSVProjection {

    Long getContractId();

    Long getPlanId();

    String getDdGenerationDate();

    String getDdSendDate();

    int getAmount();

    String getNewStatusPlan();

    String getContractStatus();

    String getContractDateOfTermination();

    String getTypeOfPayment();

    String getNotes();
}