package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.serializer.ExpenseChildrenSerializer;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.LoanType;

import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR> Mahfoud
 */
public interface ExpenseProjectionWithBuckets {

    Long getId();

    String getCode();

//    Boolean getRequireInvoice();
//
    Set<ExpensePaymentMethod> getPaymentMethods();
//
//    public Boolean getAutoDeducted();
//
//    public LoanType getLoanType();
//    public String getCaption();
      public String getLabel();
//    @JsonSerialize(using = IdLabelSerializer.class)
//    public User getManager();
//    public boolean getDisabled();
//
//    @JsonSerialize(using = ExpenseChildrenSerializer.class)
//    public List<Expense> getChildren();

    Bucket getFromCashBucket();

    Bucket getFromCreditCardBucket();

    List<Bucket> getFromCashBuckets();

    List<Bucket> getFromCreditCardBuckets();
}
