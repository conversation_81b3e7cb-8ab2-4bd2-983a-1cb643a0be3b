package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.module.type.LoanType;
import com.magnamedia.module.type.PaymentOrderStatus;
import com.magnamedia.workflow.type.PaymentRequestMethod;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Aug 16, 2020
 *         Jirra ACC-2330_QA_106
 */

public interface PaymentOrderProjection {
    @JsonSerialize(using = IdLabelSerializer.class)
    Housemaid getHousemaid();

    @JsonSerialize(using = IdLabelSerializer.class)
    ExpenseRequestTodo getExpenseRequestTodo();

    Double getAmount();

    PaymentRequestMethod getMethodOfPayment();

    PaymentOrderStatus getStatus();

    Date getDate();

    String getReason();

    boolean isWithLoan();

    String getDescription();

    LoanType getLoanType();

    @JsonSerialize(using = IdLabelSerializer.class)
    PaymentRequestPurpose getPurpose();
}
