package com.magnamedia.entity.maidsatv2.actions.employeragreement;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

/**
 * <PERSON> (May 03, 2021)
 */
@Setter
@Getter
@Entity
public class SendRequestForApprovalAction extends EmployerAgreementAction {

    private String iban;
    private String accountName;

    public String getBeneficiaryName() {
        return getMaidsAtCandidateWA() != null ? getMaidsAtCandidateWA().getEmployerName() : "";
    }

    public String getBeneficiaryMobileNumber() {
        return getMaidsAtCandidateWA() != null ? getMaidsAtCandidateWA().getEmployerPhoneNumber() : "";
    }
}