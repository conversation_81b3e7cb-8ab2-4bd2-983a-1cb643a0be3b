package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.PayrollManagerNote;
import com.magnamedia.entity.Replacement;
import com.magnamedia.module.type.MaidManagerWorkOrderStatus;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Period;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToOne;
import javax.persistence.Transient;
import org.hibernate.annotations.ColumnDefault;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 28, 2019
 * Jirra ACC-737
 */
@MappedSuperclass
public class WorkOrder extends WorkflowEntity {

    public enum MaidManagerWorkOrderFinalDecision {
        TERMINATE, FORGIVE,CHANGE_STATUS, ISSUE_WARNING_LETTER, CONERTED_TO_ABSCONDING, CONVINCED_NOT_TO_RESIGN, 
        CONVINCED_TO_RENEW, MAID_WILL_NOT_RENEW,CONVERTED_TO_MAIDSCC
    };
    
    @Enumerated(EnumType.STRING)
    private MaidManagerWorkOrderFinalDecision finalDecision;
    
    @Transient
    private String name;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Column
    private Date date;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem reason;

    @Column
    @ColumnDefault(value = "'OPEN'")
    @Enumerated(EnumType.STRING)
    private MaidManagerWorkOrderStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Replacement replacement;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PayrollManagerNote deduction;

    @Column
    private Date scheduledDate;

    @Transient
    private String delay;

    @Column(columnDefinition = "boolean default false")
    private Boolean createMohreComplaint = false;

    //Jirra ACC-737
    @Column
    private Double deductionAmount;
    
    public WorkOrder(String startTaskName) {
        super(startTaskName);
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }
    
    public PicklistItem getReason() {
        return reason;
    }

    public void setReason(PicklistItem reason) {
        this.reason = reason;
    }

    public MaidManagerWorkOrderStatus getStatus() {
        return status;
    }

    public void setStatus(MaidManagerWorkOrderStatus status) {
        this.status = status;
    }

    public Replacement getReplacement() {
        return replacement;
    }

    public void setReplacement(Replacement replacement) {
        this.replacement = replacement;
    }

    public PayrollManagerNote getDeduction() {
        return deduction;
    }

    public void setDeduction(PayrollManagerNote deduction) {
        this.deduction = deduction;
    }

    public Date getScheduledDate() {
        return scheduledDate;
    }

    public void setScheduledDate(Date scheduledDate) {
        this.scheduledDate = scheduledDate;
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return new ArrayList<>();
    }

    @Override
    public String getFinishedTaskName() {
        return "Completed";
    }

    public String getDelay() {
        if (delay == null) {
            if (this.getCreationDate() != null) {
                LocalDateTime dateTime1 = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                LocalDateTime dateTime2 = this.getCreationDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

                String s1 = getPeriod(dateTime2.toLocalDate(), dateTime1.toLocalDate());
                String s2 = getTime(dateTime2.toLocalTime(), dateTime1.toLocalTime());
                delay = s1 + s2;
            } else {
                return null;
            }
        }
        return delay;
    }

    private String getPeriod(LocalDate date1, LocalDate date2) {
        Period p = Period.between(date1, date2);
        String result = "";
        if (p.getYears() > 0) {
            result += p.getYears() + " Year ";
        }
        if (p.getMonths() > 0) {
            result += p.getMonths() + " Months ";
        }
        if (p.getDays() > 0) {
            result += p.getDays() + " Days ";
        }
        return result;
    }

    private String getTime(LocalTime time1, LocalTime time2) {

        Duration d = Duration.between(time1, time2);
        long seconds = d.getSeconds();
        String result = "";
        if (d.toHours() > 0) {
            result += d.toHours() + " Hours ";
        }
        if (((seconds % 3600) / 60) > 0) {
            result += ((seconds % 3600) / 60) + " Minutes ";
        }
//        if ((seconds % 60) > 0) {
//            result += "Seconds " + (seconds % 60);
//        }
        return result;

    }

    public void setDelay(String delay) {
        this.delay = delay;
    }

    public String getName() {
        return this.name = this.getHousemaid().getName();
    }

    public void setName(String name) {
        this.name = name;
    }

    public MaidManagerWorkOrderFinalDecision getFinalDecision() {
        return finalDecision;
    }

    public void setFinalDecision(MaidManagerWorkOrderFinalDecision finalDecision) {
        this.finalDecision = finalDecision;
    }

    public Boolean getCreateMohreComplaint() {
        return createMohreComplaint;
    }

    public void setCreateMohreComplaint(Boolean createMohreComplaint) {
        this.createMohreComplaint = createMohreComplaint;
    }

    public Double getDeductionAmount() {
        return deductionAmount;
    }

    public void setDeductionAmount(Double deductionAmount) {
        this.deductionAmount = deductionAmount;
    }
}

