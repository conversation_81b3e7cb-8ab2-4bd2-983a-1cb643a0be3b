package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Housemaid;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import org.hibernate.annotations.ColumnDefault;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 24, 2019
 * Jirra ACC-737
 */
@Entity
public class MaidManagerActionLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;
    
    @Column
    private Date actionDate;

    @Lob
    @Column
    private String notes;

    @Column
    private Boolean deductionRemoved;

    @Column
    private Boolean failedInterviewRemoved;

    @Column
    @ColumnDefault(value = "0")
    private Integer numberOfFailedInterviewToForgive;

    @Column
    @ColumnDefault(value = "0")
    private Integer numberOfReplacementsToForgive;
    
    @Column
    @ColumnDefault(value = "0")
    private Integer numberOfAvailableRemainingDaysToForgive;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private MaidManagerWorkOrder workOrder;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem actionType;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Date getActionDate() {
        return actionDate;
    }

    public void setActionDate(Date ActionDate) {
        this.actionDate = ActionDate;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public Boolean getDeductionRemoved() {
        return deductionRemoved;
    }

    public void setDeductionRemoved(Boolean deductionRemoved) {
        this.deductionRemoved = deductionRemoved;
    }

    public MaidManagerWorkOrder getWorkOrder() {
        return workOrder;
    }

    public void setWorkOrder(MaidManagerWorkOrder workOrder) {
        this.workOrder = workOrder;
    }
    
    public Boolean getFailedInterviewRemoved() {
        return failedInterviewRemoved;
    }

    public void setFailedInterviewRemoved(Boolean failedInterviewRemoved) {
        this.failedInterviewRemoved = failedInterviewRemoved;
    }

    public PicklistItem getActionType() {
        return actionType;
    }

    public void setActionType(PicklistItem actionType) {
        this.actionType = actionType;
    }

    public Integer getNumberOfFailedInterviewToForgive() {
        return numberOfFailedInterviewToForgive != null ? numberOfFailedInterviewToForgive : 0;
    }

    public void setNumberOfFailedInterviewToForgive(Integer numberOfFailedInterviewToForgive) {
        this.numberOfFailedInterviewToForgive = numberOfFailedInterviewToForgive;
    }

    public Integer getNumberOfAvailableRemainingDaysToForgive() {
        return numberOfAvailableRemainingDaysToForgive != null ? numberOfAvailableRemainingDaysToForgive : 0;
    }

    public void setNumberOfAvailableRemainingDaysToForgive(Integer numberOfAvailableRemainingDaysToForgive) {
        this.numberOfAvailableRemainingDaysToForgive = numberOfAvailableRemainingDaysToForgive;
    }

    public Integer getNumberOfReplacementsToForgive() {
        return numberOfReplacementsToForgive != null ? numberOfReplacementsToForgive : 0;
    }

    public void setNumberOfReplacementsToForgive(Integer numberOfReplacementsToForgive) {
        this.numberOfReplacementsToForgive = numberOfReplacementsToForgive;
    }
}
