package com.magnamedia.entity.workflow;

import com.magnamedia.module.type.DoctorWorkOrderType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jul 28, 2019
 * Jirra ACC-737
 */
@Entity
public class DoctorWorkOrder extends WorkOrder {

    @Column
    @Enumerated(EnumType.STRING)
    private DoctorWorkOrderType type;
    
    public DoctorWorkOrder() {
        super("");
    }

    public DoctorWorkOrderType getType() {
        return type;
    }

    public void setType(DoctorWorkOrderType type) {
        this.type = type;
    }
    
    
}

