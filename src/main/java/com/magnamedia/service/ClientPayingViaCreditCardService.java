package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.epayment.EPaymentTransaction;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.helper.epayment.EPaymentProvider;
import com.magnamedia.core.helper.epayment.ETransactionStatus;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.AppActionType;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.CcAppCmsTemplate;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.report.CheckOutErrorCodeIssueReport;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

//ACC-4715
@Service
public class ClientPayingViaCreditCardService {
    private static final Logger logger = Logger.getLogger(ClientPayingViaCreditCardService.class.getName());
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private Shortener shortener;
    @Autowired
    private FlowEventConfigRepository flowEventConfigRepository;
    @Autowired
    private DisablePushNotificationRepository disablePushNotificationRepository;
    @Autowired
    private FlowSubEventConfigRepository flowSubEventConfigRepository;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private PushNotificationHelper pushNotificationHelper;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private SwitchingNationalityService switchingNationalityService;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ContractPaymentService contractPaymentService;
    @Autowired
    private AccountingEPaymentService accountingEPaymentService;
    @Autowired
    private RecurringCreditCardPaymentsIssueRepository checkoutErrorCodeForSubFlowRepository;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    private ExtensionFlowService extensionFlowService;
    @Autowired
    private MaidVisaFailedMedicalCheckService maidVisaFailedMedicalCheckService;

    public void startNewFlow(
            ContractPaymentTerm contractPaymentTerm,
            FlowEventConfig.FlowEventName flowEventName,
            FlowSubEventConfig.FlowSubEventName flowSubEventName,
            DirectDebit directDebit,
            List<ContractPayment> contractPayments) {

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(flowEventName);
        FlowSubEventConfig flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig(
                flowSubEventName, flowEventConfig);

        Map<String, Object> map = new HashMap<>();
        map.put("trials", 0);
        map.put("reminders", 1);
        map.put("lastExecutionDate", new Date());

        FlowProcessorEntity flow = flowProcessorService.createFlowProcessor(
                flowEventConfig, flowSubEventConfig, contractPaymentTerm, map);
        if (flow == null) return;

        if (flowSubEventName.equals(FlowSubEventConfig.FlowSubEventName.DD_Rejection)) {
            flow.setDirectDebit(directDebit);

        } else if (!startInitialFlow(flow, contractPayments, false, false)) return;

        flow = flowProcessorEntityRepository.findOne(flow.getId());
        flowProcessorService.processFlowSubEventConfig(flow);
    }

    public boolean startInitialFlow(FlowProcessorEntity flow, List<ContractPayment> contractPayments, boolean forceSwitch, boolean fromErp) {
        try {
            // ACC-5791
            Contract c = flow.getContract();
            if (c.isOneMonthAgreement()) {
                c.setOneMonthAgreement(false);
                Setup.getRepository(ContractRepository.class)
                        .save(c);
            }

            ContractPaymentTerm cpt = flow.getContractPaymentTerm();
            logger.log(Level.INFO, "start initial flow cpt id: {0}", cpt.getId());
            ContractPaymentConfirmationToDo t = contractPaymentConfirmationToDoRepository
                    .findFirstByContractPaymentTerm_ContractAndSourceAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
                            cpt.getContract(),
                            ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card);
            DateTime f = new DateTime().withDayOfMonth(1).withTimeAtStartOfDay();

            if (t == null || t.isPayingViaCreditCard() || t.getCreationDate().getTime() < f.toDate().getTime()) {
                if (contractPayments == null)
                    contractPayments = contractPaymentService.getUniqueAndSortedPayments(c,
                            getUnReceivedContractPayment(cpt, false), null);

                if (!contractPayments.isEmpty()) {
                    logger.info("Has payments not received yet -> start initial flow");
                    flow.setContractPaymentConfirmationToDo(
                            addConfirmationTodoForPayment(contractPayments, fromErp));

                    int startMonthlyReminderDay = 8;
                    if (flow.getFlowEventConfig().hasTag("monthly_reminder_paying_cc_start_before_x_days")) {
                        startMonthlyReminderDay = Integer.parseInt(
                                flow.getFlowEventConfig().getTagValue("monthly_reminder_paying_cc_start_before_x_days").getValue());
                    }

                    if (new LocalDate().isBefore(
                            new LocalDate(flow.getContract().getPaidEndDate()).minusDays(startMonthlyReminderDay))) {
                        flow.setLastExecutionDate(new DateTime(flow.getContract().getPaidEndDate())
                                .minusDays(startMonthlyReminderDay).toDate());
                    }
                    flowProcessorEntityRepository.save(flow);
                } else {
                    logger.info("All payments received -> start reminder flow");
                    validateStopFlow(flow, true);
                }
            }

            List<DirectDebitStatus> notAllowedStatuses = new ArrayList<>(Arrays.asList(
                    DirectDebitStatus.CANCELED,
                    DirectDebitStatus.PENDING_FOR_CANCELLATION));

            if (forceSwitch || !directDebitRepository.existsByContractPaymentTermAndCategoryBAndStatusIn(
                    flow.getContractPaymentTerm(),
                    Arrays.asList(DirectDebitCategory.B),
                    Arrays.asList(DirectDebitStatus.CONFIRMED))) {

                Setup.getApplicationContext()
                        .getBean(ContractService.class)
                        .updatePayingViaCreditCardFlag(c, true);
            }

            // SD-51941
            if (!forceSwitch) {
                notAllowedStatuses.add(DirectDebitStatus.CONFIRMED);
            }

            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                    .cancelDdsAndStopRejectionFlow(cpt,
                            DirectDebitCancellationToDoReason.CLIENT_PAYING_VIA_Credit_Card_FLOW,
                            notAllowedStatuses, contractPayments);
            if (flow.isStopped() || flow.isCompleted()) return false;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    public List<ContractPayment> getUnReceivedContractPayment(ContractPaymentTerm cpt, boolean withAllPayments) {
        List<DirectDebitStatus> notAllowedStatuses = Arrays.asList(
                DirectDebitStatus.EXPIRED,
                DirectDebitStatus.CANCELED,
                DirectDebitStatus.PENDING_FOR_CANCELLATION,
                DirectDebitStatus.CONFIRMED);

        List<ContractPayment> contractPayments = new ArrayList<>();
        contractPayments.addAll(getAllDdbPayments(cpt, withAllPayments, notAllowedStatuses)); // all DDB
        contractPayments.addAll(getAllDdaPayments(cpt, withAllPayments, notAllowedStatuses)); // all DDA

        return contractPayments;
    }

    public List<ContractPayment> getAllDdbPayments(
            ContractPaymentTerm cpt,
            boolean withAllPayments,
            List<DirectDebitStatus> notAllowedStatuses) {

        //monthly payment
        SelectQuery<ContractPayment> query = new SelectQuery<>(ContractPayment.class);
        query.filterBy("contractPaymentTerm.id", "=", cpt.getId());
        query.filterBy("date", ">=", new LocalDate().dayOfMonth().withMinimumValue().toDate());
        query.filterBy("paymentMethod", "=", PaymentMethod.DIRECT_DEBIT);
        query.filterBy("directDebit.category", "=", DirectDebitCategory.B);
        query.filterBy("paymentType.code", "=", "monthly_payment");
        query.sortBy("creationDate", false);

        if (!withAllPayments) {
            query.filterBy("date", "<=", new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());
            query.filterBy("directDebit.status", "not in", notAllowedStatuses);
        }

        return query.execute();
    }

    public List<ContractPayment> getAllDdaPayments(
            ContractPaymentTerm cpt,
            boolean withAllPayments,
            List<DirectDebitStatus> notAllowedStatuses) {

        SelectQuery<ContractPayment>  query = new SelectQuery<>(ContractPayment.class);
        query.filterBy("contractPaymentTerm.id", "=", cpt.getId());
        query.filterBy("paymentMethod", "=", PaymentMethod.DIRECT_DEBIT);
        query.filterBy("directDebit.category", "=", DirectDebitCategory.A);
        query.sortBy("creationDate", false);

        if (!withAllPayments) {
            query.filterBy("date", "<", new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate());
            query.filterBy("directDebit.status", "not in", notAllowedStatuses);
            query.filterBy("directDebit.MStatus", "not in", notAllowedStatuses);
        }

        List<ContractPayment> l =  query.execute();

        if (withAllPayments) return l;
        logger.info("contractPayments size" + l.size());

        // ACC-9222 Handle All Payments in contract first month, non-Monthly and start on '0' to the flow
        return handleCurrentPaymentsAlreadyCreated(cpt, l);
    }

    public void startReminderFlow(
            ContractPaymentTerm cpt,
            FlowEventConfig.FlowEventName flowEventName,
            FlowSubEventConfig.FlowSubEventName flowSubEventName,
            int startMonthlyReminderDay) {

        logger.log(Level.INFO, "cpt id : {0}", cpt.getId());

        try {
            if (skipStartMonthlyReminderFlow(cpt)) return;

            FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(flowEventName);
            FlowSubEventConfig flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig(
                    flowSubEventName, flowEventConfig);

            Map<String, Object> map = new HashMap<>();
            map.put("trials", 0);
            map.put("reminders", 1);
            map.put("lastExecutionDate", new LocalDate(cpt.getContract().getPaidEndDate()).minusDays(startMonthlyReminderDay).toDate());

            flowProcessorService.createFlowProcessor(
                    flowEventConfig,flowSubEventConfig, cpt, map);

            Setup.getApplicationContext()
                    .getBean(DirectDebitGenerationPlanService.class)
                    .cancelAllMonthlyPlans(cpt.getContract().getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean skipStartMonthlyReminderFlow(ContractPaymentTerm cpt) {

        if (cpt.getContract().isMaidVisa() &&
                maidVisaFailedMedicalCheckService.checkFailedMedicalsForPayingViaCc(cpt)) {
            return true;
        }

        if (flowProcessorEntityRepository.existsOnlineReminderCoverNextMonthlyPayment(
                cpt.getContract(),
                new LocalDate(cpt.getContract().getPaidEndDate()).plusDays(1).toDate(),
                new LocalDate(cpt.getContract().getPaidEndDate()).plusMonths(1).toDate())) {
            logger.info("There is running reminder flow -> skip");
            return true;
        }

        if (directDebitRepository.existsDDConfirmedCoveredPaymentWithoutAmount(
                cpt.getContract().getId(), AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                new LocalDate(cpt.getContract().getPaidEndDate()).plusDays(1).toDate(),
                new LocalDate(cpt.getContract().getPaidEndDate()).plusMonths(1).toDate())) {

            logger.info("Contract has Dd Confirmed for next Month -> exiting");
            return true;
        }

        if (new DateTime().isAfter(new DateTime(cpt.getContract().getPaidEndDate())) &&
                skipReminderFlow(null, cpt)) {
            logger.info("Next payment flagged as recurring -> exiting");
            return true;
        }

        return false;
    }

    public FlowProcessorEntity startPayingViaRecurringSubFlows(
            ContractPaymentTerm cpt,
            FlowEventConfig.FlowEventName flowEventName,
            FlowSubEventConfig.FlowSubEventName flowSubEventName,
            ContractPaymentConfirmationToDo todo,
            Payment p) {

        logger.log(Level.INFO, "cpt id : {0}", cpt.getId());

        try {
            Date paymentDate = p != null ? p.getDateOfPayment() :
                    todo != null && !todo.getContractPaymentList().isEmpty() ?
                            todo.getContractPaymentList().get(0).getPaymentDate() :
                            null;
            Double paymentAmount = p != null ? p.getAmountOfPayment() :
                    todo != null && !todo.getContractPaymentList().isEmpty() ?
                            todo.getContractPaymentList().get(0).getAmount() :
                            null;
            if (paymentDate != null && paymentAmount != null &&
                    paymentRepository.existReceivedPaymentByContractAndDateOfPaymentBetween(
                            cpt.getContract(),
                            paymentDate,
                            new LocalDate(paymentDate).plusMonths(1).minusDays(1).toDate(),
                            AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                            paymentAmount)) {
                logger.info("payment received -> existing");
                return null;
            }

            if (todo != null && todo.isShowOnERP()) {
                logger.info("todo isShowOnERP -> existing");
                return null;
            }

            if ((cpt.getSourceId() == null || (p != null && p.getStatus().equals(PaymentStatus.DELETED))) && todo != null) {
                todo.setDisabled(true);
                todo = contractPaymentConfirmationToDoRepository.saveAndFlush(todo);
                logger.info("token or payment deleted -> disable old todo");
            }

            if ((todo == null ||  todo.isDisabled()) && p != null &&
                    !Arrays.asList(PaymentStatus.RECEIVED, PaymentStatus.DELETED).contains(p.getStatus())){
                todo = contractPaymentConfirmationToDoService.createToDoForRecurringPaymentIfNotExists(p, cpt);
            }

            Map<String, Object> map = new HashMap<>();
            map.put("trials", 0);
            map.put("reminders", 1);
            map.put("lastExecutionDate", new Date());
            map.put("todo", todo != null && !todo.isDisabled() ? todo : null);

            FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(flowEventName);
            FlowSubEventConfig flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig(
                    flowSubEventName, flowEventConfig);

            return flowProcessorService.createFlowProcessor(flowEventConfig, flowSubEventConfig, cpt, map);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public boolean validateSkippingFlow(FlowProcessorEntity entity) {
        if (Arrays.asList(FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                FlowSubEventConfig.FlowSubEventName.EXPIRED_CARD,
                FlowSubEventConfig.FlowSubEventName.TOKEN_DELETED).contains(entity.getCurrentSubEvent().getName()) &&
        !entity.getContract().isPayingViaCreditCard()) {
            logger.info("The flag turned to false -> stop the flow" );
            entity.setStopped(true);
            flowProcessorEntityRepository.save(entity);
            return true;
        }


        boolean shouldStopFlow = false;
        switch (entity.getCurrentSubEvent().getName()) {
            case INITIAL_FLOW_FOR_DDA:
            case INITIAL_FLOW_FOR_DDB:
                shouldStopFlow = entity.getContractPaymentConfirmationToDo() == null ||
                        entity.getContractPaymentConfirmationToDo().isShowOnERP() ||
                        entity.getContractPaymentConfirmationToDo().isDisabled();
                break;
            case EXPIRED_CARD:
            case MONTHLY_REMINDER:
            case TOKEN_DELETED:
                shouldStopFlow = flowProcessorService.nextMonthPaymentReceived(entity.getContract());
                break;
            case ACCOUNT_ISSUE:
            case EXCEEDING_DAILY_LIMITS:
            case CC_OTHER_ISSUES:
            case INSUFFICIENT_FUNDS:
                shouldStopFlow = flowProcessorService.currentMonthPaymentReceived(entity.getContract());
                break;
        }

        logger.info("isReceived: " + shouldStopFlow);
        if (shouldStopFlow) {
            validateStopFlow(entity, true);
            return true;
        }

        boolean nextDdConfirmed = flowProcessorService.nextMonthDdConfirmed(entity.getContract());
        logger.info("nextDdConfirmed: " + nextDdConfirmed);

        if (nextDdConfirmed) {
            validateStopFlow(entity, false);
            return true;
        }

        return false;
    }

    private void validateStopFlow(FlowProcessorEntity entity, boolean isCompleted) {
        logger.log(Level.INFO, "entity id: {0}; Flow stopping because the payment status changed", entity.getId());

        switch (entity.getCurrentSubEvent().getName()) {
            case INITIAL_FLOW_FOR_DDA:
            case INITIAL_FLOW_FOR_DDB:
                switchToMonthReminder(entity);

                // ACC-5791
                Setup.getApplicationContext()
                        .getBean(OneMonthAgreementFlowService.class)
                        .validateSwitchToProrated(entity.getContract());
                break;
        }

        // ACC-8019 this case when received payment or there is DD confirmed
        if (FlowProcessorService.recurringFailureFlowsWithExpiredCard.contains(entity.getCurrentSubEvent().getName()) &&
                entity.getContractPaymentConfirmationToDo() != null && !entity.getContractPaymentConfirmationToDo().isDisabled()) {
            entity.getContractPaymentConfirmationToDo().setDisabled(true);
            contractPaymentConfirmationToDoRepository.save(entity.getContractPaymentConfirmationToDo());
        }

        if (isCompleted) {
            entity.setCompleted(true);
        } else {
            entity.setStopped(true);
        }
        flowProcessorEntityRepository.save(entity);
    }

    public boolean switchToMonthReminder(FlowProcessorEntity entity) {
        if (directDebitRepository.existsByContractPaymentTermAndCategoryBAndStatusIn(
                entity.getContractPaymentTerm(), Collections.singletonList(DirectDebitCategory.B),
                Collections.singletonList(DirectDebitStatus.CONFIRMED))) return false;

        logger.info("contract id: " + entity.getContract().getId());

        if (!entity.getContract().isPayingViaCreditCard()) {

            Setup.getApplicationContext()
                    .getBean(ContractService.class)
                    .updatePayingViaCreditCardFlag(entity.getContract(), true);
        }

        int sendDdSigningOfferDay = 1;
        if (entity.getFlowEventConfig().hasTag("day_of_send_dd_signing_offer")) {
            sendDdSigningOfferDay = Integer.parseInt(
                    entity.getFlowEventConfig().getTagValue("day_of_send_dd_signing_offer").getValue());
        }

        FlowEventConfig flowEventConfig = flowEventConfigRepository.findByName(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);
        FlowSubEventConfig flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig(
                FlowSubEventConfig.FlowSubEventName.DD_SIGNING_OFFER, flowEventConfig);

        Map<String, Object> map = new HashMap<>();
        map.put("trials", 0);
        map.put("reminders", 1);
        map.put("lastExecutionDate", new DateTime().plusMonths(1)
                .withDayOfMonth(sendDdSigningOfferDay).toDate());

        flowProcessorService.createFlowProcessor(
                flowEventConfig, flowSubEventConfig, entity.getContractPaymentTerm(), map);

        // ACC-6182
        Setup.getApplicationContext()
                .getBean(DirectDebitGenerationPlanService.class)
                .cancelAllMonthlyPlans(entity.getContract().getId());

        return true;
    }

    @Transactional
    public ContractPaymentConfirmationToDo addConfirmationTodoForPayment(List<ContractPayment> contractPayments) {
        return addConfirmationTodoForPayment(contractPayments, false);
    }

    @Transactional
    public ContractPaymentConfirmationToDo addConfirmationTodoForPayment(List<ContractPayment> contractPayments, boolean fromErp) {

        logger.log(Level.INFO, "ContractPayment size: {0}", contractPayments.size());

        ContractPaymentConfirmationToDo todo = new ContractPaymentConfirmationToDo();
        todo.setContractPaymentTerm(contractPayments.get(0).getContractPaymentTerm());
        todo.setSource(ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card);
        todo.setPaymentType(contractPayments.get(0).getPaymentType());
        todo.setPaymentMethod(PaymentMethod.CARD);
        todo.setPayingOnline(true);
        todo.setDescription(contractPayments.get(0).getDescription());
        todo.setAttachments(contractPayments.get(0).getAttachments());
        todo.setPayingViaCreditCard(fromErp);

        contractPayments.forEach(cp -> {
            logger.log(Level.INFO, "cp id: {0}", cp.getId());

            ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
            wrapper.setContractPaymentConfirmationToDo(todo);
            wrapper.setPaymentDate(cp.getDate());
            wrapper.setProrated(cp.getIsProRated());
            wrapper.setInitial(cp.getIsInitial());
            wrapper.setIncludeWorkerSalary(cp.getIncludeWorkerSalary());
            Double amount = cp.getAmount();
            Double discountAmount = cp.getDiscountAmount();
            Boolean includeWorkerSalary = cp.getIncludeWorkerSalary();
            Double workerSalary = cp.getWorkerSalary();
            wrapper.setAffectedByAdditionalDiscount(cp.getAdditionalDiscountAmount() != null &&
                    cp.getAdditionalDiscountAmount() > 0.0);
            wrapper.setMoreAdditionalDiscount(cp.getMoreAdditionalDiscount());

            // ACC-8422
            if (PaymentHelper.isMonthlyPayment(cp.getPaymentType())) {
                Map<String, Object> m = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(
                        cp.getContractPaymentTerm(), new LocalDate(cp.getDate()));
                if ((boolean) m.getOrDefault("includeWorkerSalary", false)) {
                    logger.info("override cp: " + cp.getId());
                    amount = (Double) m.get("amount");
                    includeWorkerSalary = true;
                    workerSalary = (Double) m.get("workerSalary");
                    discountAmount = (Double) m.getOrDefault("discountAmount", 0D);
                    wrapper.setAffectedByAdditionalDiscount((Boolean) m.get("affectedByAdditionalDiscount"));
                    wrapper.setMoreAdditionalDiscount((Double) m.getOrDefault("moreAdditionalDiscount", 0.0));
                }
            }

            wrapper.setActualReceivedAmount(amount);
            wrapper.setAmount(amount);
            wrapper.setDiscountAmount(discountAmount);
            wrapper.setIncludeWorkerSalary(includeWorkerSalary);
            wrapper.setWorkerSalary(workerSalary);
            wrapper.setVatPaidByClient(cp.getVatPaidByClient());
            wrapper.setDescription(cp.getDescription());
            wrapper.setPaymentType(cp.getPaymentType());
            wrapper.setSubType(cp.getSubType());

            todo.getContractPaymentList().add(wrapper);
        });

        contractPaymentConfirmationToDoService.createConfirmationToDo(todo);
        return contractPaymentConfirmationToDoRepository.findOne(todo.getId());
    }

    public void createPaymentAfterPaidSuccess(ContractPaymentConfirmationToDo toDo) {
        logger.info("create new payment, todo id: " + toDo.getId());
        Payment payment = toDo.getContractPaymentList().get(0).getGeneratedPaymentId() != null ?
                Setup.getRepository(PaymentRepository.class).findOne(toDo.getContractPaymentList().get(0).getGeneratedPaymentId()) :
                null;

        if (payment != null) {
            try {
                payment.setStatus(PaymentStatus.RECEIVED);
                paymentService.forceUpdatePayment(payment);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            toDo.getContractPaymentList().forEach(wrapper -> {
                try {
                    if (paymentService.changeRecurringPaymentToReceived(wrapper)) return;

                    contractPaymentConfirmationToDoService.createPayment(
                            wrapper, toDo.getAttachments(),
                            PaymentStatus.RECEIVED, true);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }

        disableOldNotification(toDo.getContractPaymentTerm().getContract());
    }

    public void disableOldNotification(Contract c) {
        List<PushNotification> clientNotifications = disablePushNotificationRepository
                .findActiveNotificationsByDDMessagingType(
                        c.getClient().getId().toString(),
                        Collections.singletonList(DDMessagingType.ClientsPayingViaCreditCard),
                        c.getId());

        pushNotificationHelper.stopDisplaying(clientNotifications);
    }

    public ContractPaymentConfirmationToDo messagingFilter(
            SelectQuery<DDMessaging> query,
            FlowProcessorEntity entity) {

        ContractPaymentConfirmationToDo t = null;

        switch (entity.getCurrentSubEvent().getName()) {
            case MONTHLY_REMINDER:
            case TOKEN_DELETED:
            case EXPIRED_CARD:
                LocalDate startDate = entity.getContract().isOneMonthAgreement() ?
                        new LocalDate(entity.getContractPaymentTerm().getContract().getPaidEndDate()).plusDays(1) :
                        new LocalDate(entity.getContractPaymentTerm().getContract().getPaidEndDate()).plusMonths(1).dayOfMonth().withMinimumValue();

                t = createTodoIfNotExists(entity.getContractPaymentTerm(), startDate);
                if (entity.getContractPaymentConfirmationToDo() != null) return null;
                break;
            case DD_Rejection:
                if (entity.getDirectDebit() != null) {
                    query.filterBy("rejectCategory", "=", entity.getDirectDebit().getRejectCategory());
                }
                break;
            case INITIAL_FLOW_FOR_DDA:
            case INITIAL_FLOW_FOR_DDB:
                if (entity.getContractPaymentConfirmationToDo() != null) {
                    query.filterBy("paymentStructure", "=",
                            entity.getContractPaymentConfirmationToDo().getContractPaymentList().size() > 1 ?
                                    DDMessagingPaymentStructure.MULTIPLE_PAYMENTS :
                                    DDMessagingPaymentStructure.ONE_PAYMENT);
                }
                break;
        }

        return t;
    }

    @Transactional
    public ContractPaymentConfirmationToDo createTodoIfNotExists(ContractPaymentTerm cpt, LocalDate date) {
        if(cpt.getContract().isOneMonthAgreement()) {
            return Setup.getApplicationContext()
                    .getBean(OneMonthAgreementFlowService.class)
                    .createTodoIfNotExists(cpt);
        }


        try {
            logger.log(Level.INFO, "cpt id : {0}", cpt.getId());

            ContractPaymentConfirmationToDo todo = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                    .findByCptAndSourceAndPaymentTypeAndDate(cpt,
                            ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card,
                            date.dayOfMonth().withMinimumValue().toDate(),
                            date.dayOfMonth().withMaximumValue().toDate());


            if (todo != null) {
                return todo;
            }

            List<ContractPayment> contractPayments = contractPaymentService.getUniqueAndSortedPayments(
                    cpt.getContract(),
                    contractPaymentRepository.findMatchedContractPayment(
                            cpt.getContract(), AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE ,
                            date.dayOfMonth().withMinimumValue().toDate(),
                            date.dayOfMonth().withMaximumValue().toDate()),
                    null);

            if (contractPayments.isEmpty()) {
                return contractPaymentConfirmationToDoService.createMonthlyCreditCardForNextMonth(
                        cpt.getContract(), ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card);

            }

            return addConfirmationTodoForPayment(contractPayments);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public Map<String, Object> getFutureDirectDebitAndContractPayment(ContractPaymentTerm cpt) {

        // ACC-6509
        if (Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .hasActiveDDWithoutRejected(cpt.getContract()))
            return new HashMap<String, Object>(){{
                put("payments", new ArrayList<>());
                put("directDebits", new ArrayList<>());
            }};

        DateTime d = paymentService.getLastReceivedMonthlyPaymentDate(cpt.getContract());

        SelectQuery<ContractPayment> query = new SelectQuery<>(ContractPayment.class);
        query.filterBy("contractPaymentTerm", "=", cpt);
        query.filterBy("paymentMethod", "=", PaymentMethod.DIRECT_DEBIT);
        if (d != null) query.filterBy("date", ">=", d.plusMonths(1).dayOfMonth().withMinimumValue().toDate());
        query.sortBy("creationDate", false);

        List<ContractPayment> payments = contractPaymentService.getUniqueAndSortedPayments(
                cpt.getContract(), query.execute(), d == null ? null : d.dayOfMonth().withMinimumValue());

        payments.forEach(cp -> cp.setDirectDebit(null));

        return new HashMap<String, Object>() {{
            put("payments", payments);
            put("directDebits", Setup.getApplicationContext().getBean(DirectDebitService.class)
                    .getDirectDebitsOfPayments(payments, cpt, false));
        }};
    }

    // ACC-6162
    public boolean reactivateInitialFlowViaConfirmationTodo(ContractPaymentTerm cpt, ContractPaymentConfirmationToDo toDo) {
        logger.info("cpt id : " + cpt.getId());

        if (flowProcessorService.existsRunningFlow(cpt.getContract(),
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                Arrays.asList(
                        FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                        FlowSubEventConfig.FlowSubEventName.EXPIRED_CARD)) ||
                cpt.getContract().isOneMonthAgreement()) return false;

        List<FlowProcessorEntity> flows =
                flowProcessorEntityRepository.findOnlineCardTodosRelatedToInitialFlowStopped(
                        cpt.getContract(), toDo,
                        Arrays.asList(
                                FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                                FlowSubEventConfig.FlowSubEventName.EXPIRED_CARD),
                        PageRequest.of(0, 1));
        if (flows.isEmpty()) return false;

        ContractPaymentConfirmationToDo oldTodo = flows.get(0).getContractPaymentConfirmationToDo();
        if (!toDo.getId().equals(oldTodo.getId()) &&
                toDo.getContractPaymentList()
                        .stream()
                        .noneMatch(w -> oldTodo.getContractPaymentList()
                                .stream()
                                .anyMatch(w1 -> new LocalDate(w.getPaymentDate()).toString("yyyy-MM")
                                        .equals(new LocalDate(w1.getPaymentDate()).toString("yyyy-MM")) &&
                                        w.getPaymentType().equals(w1.getPaymentType())))) {
            logger.info("None match with old todo id: " + oldTodo.getId());
            return false;
        }

        logger.info("Old todo id: " + oldTodo.getId());

        reactivateInitialFlow(flows.get(0), getUnReceivedContractPaymentViaToDo(oldTodo, toDo));

        return true;
    }

    public void reactivateInitialFlowViaStoppedFlow(FlowProcessorEntity f) {
        if (f.getContractPaymentConfirmationToDo() == null) return;
        logger.info("cpt id : " + f.getContractPaymentTerm().getId() +
                "; Old todo id: " + f.getContractPaymentConfirmationToDo().getId());
        reactivateInitialFlow(f, getUnReceivedContractPaymentViaPayments(f.getContractPaymentConfirmationToDo()));
    }

    private void reactivateInitialFlow(FlowProcessorEntity f, List<ContractPayment> unReceivedPayments) {
        logger.info("flow id : " + f.getId());
        if (!f.getContractPaymentConfirmationToDo().isDisabled()) {
            f.getContractPaymentConfirmationToDo().setDisabled(true);
            contractPaymentConfirmationToDoRepository.saveAndFlush(f.getContractPaymentConfirmationToDo());
        }

        if (unReceivedPayments.isEmpty()) {
            switchToMonthReminder(f);
            logger.info("All payments on old todo id: " + f.getContractPaymentConfirmationToDo().getId() + " received");
        } else {
            startNewFlow(f.getContractPaymentTerm().getContract().getActiveContractPaymentTerm(), f.getFlowEventConfig().getName(),
                    f.getCurrentSubEvent().getName(), null, unReceivedPayments);
        }
    }

    private List<ContractPayment> getUnReceivedContractPaymentViaToDo(ContractPaymentConfirmationToDo toDo, ContractPaymentConfirmationToDo newTodo) {

        return getUnReceivedContractPaymentViaPayments(toDo)
                .stream()
                .filter(c -> newTodo.getContractPaymentList().stream().noneMatch(w1 -> c.getAmount().equals(w1.getAmount()) &&
                                new LocalDate(c.getDate()).toString("yyyy-MM")
                                        .equals(new LocalDate(w1.getPaymentDate()).toString("yyyy-MM")) &&
                                c.getPaymentType().equals(w1.getPaymentType())))
                .collect(Collectors.toList());
    }

    private List<ContractPayment> getUnReceivedContractPaymentViaPayments(ContractPaymentConfirmationToDo toDo) {

        return toDo.getContractPaymentList()
                .stream()
                .filter(w -> !paymentRepository.existsByStatusAndContractAndDateOfPaymentAndTypeOfPayment(
                                PaymentStatus.RECEIVED, toDo.getContractPaymentTerm().getContract(), w.getPaymentDate(), w.getPaymentType()))
                .map(ContractPaymentWrapper::initContractPaymentProps)
                .collect(Collectors.toList());
    }

    public void stopFlowAfterPaymentStatusChanged(ContractPaymentConfirmationToDo t) {
        logger.info("toDo id: " + t.getId());

        SelectQuery<FlowProcessorEntity> q = new SelectQuery<>(FlowProcessorEntity.class);
        q.filterBy("contractPaymentConfirmationToDo", "=", t);
        q.filterBy("currentSubEvent.name", "in", Arrays.asList(
                FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA,
                FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDB));
        q.filterBy("stopped", "=", false);
        q.filterBy("completed", "=", false);
        q.setLimit(1);

        List<FlowProcessorEntity> l = q.execute();
        if (l.isEmpty()) return;
        logger.info("Initial flow id: " + l.get(0).getId() + " one payment or more received -> stop the flow");
        l.get(0).setStopped(true);
        flowProcessorEntityRepository.save(l.get(0));

        if (!switchToMonthReminder(l.get(0))) return;

        DateTime d = Setup.getApplicationContext().getBean(PaymentService.class)
                .getLastReceivedMonthlyPaymentDate(t.getContractPaymentTerm().getContract());

        if (d != null && d.isAfter(new DateTime().plusMonths(1)
                .dayOfMonth()
                .withMinimumValue()
                .withTimeAtStartOfDay()
                .minusHours(1))) return;

        int startMonthlyReminderDay = 8;
        FlowEventConfig flowEventConfig = flowEventConfigRepository
                .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);
        if (flowEventConfig.hasTag("monthly_reminder_paying_cc_start_before_x_days")) {
            startMonthlyReminderDay = Integer.parseInt(
                    flowEventConfig.getTagValue("monthly_reminder_paying_cc_start_before_x_days").getValue());
        }

        startReminderFlow(
                t.getContractPaymentTerm(),
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                startMonthlyReminderDay);

    }

    public Map<String, Object> getInitialFlowPayments(ContractPaymentTerm cpt, boolean checkForConfirmedDDb) {

        Map<String, Object> m = new HashMap<String, Object>() {{
            put("startPreventCreateOtherDds", false);
        }};

        // ACC-8662
        // Check and Start Prevent Create Other Dds
        if(Setup.getApplicationContext()
                .getBean(ContractService.class)
                .validateAndStartPreventCreateOtherDds(cpt, true)) {

            m.put("startPreventCreateOtherDds", true);
            return m;
        }

        List<ContractPayment> contractPayments = Setup.getApplicationContext().getBean(ContractPaymentService.class)
                .getUniqueAndSortedPayments(cpt.getContract(), getUnReceivedContractPayment(cpt, false), null);

        if (contractPayments.isEmpty() && checkForConfirmedDDb && Setup.getRepository(DirectDebitRepository.class)
                .existsByContractPaymentTermAndCategoryBAndStatusIn(
                        cpt, Arrays.asList(DirectDebitCategory.B),
                        Arrays.asList(DirectDebitStatus.CONFIRMED)))
            throw new BusinessException("All payments received and the client has confirmed DDB");

        if (contractPayments.isEmpty()) {
            DateTime lastPayment = Setup.getApplicationContext()
                    .getBean(PaymentService.class)
                    .getLastReceivedMonthlyPaymentDate(cpt.getContract());
            logger.info("There are no contract payments start create new payments");

            if (lastPayment == null ||
                    lastPayment.isBefore(new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay())) {
                contractPayments.add(contractPaymentConfirmationToDoService.createContractPaymentForMonthlyPayment(
                        cpt, new LocalDate().dayOfMonth().withMinimumValue().toDate()));
            }

            if (lastPayment == null ||
                    lastPayment.isBefore(new DateTime().plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay())) {
                contractPayments.add(contractPaymentConfirmationToDoService.createContractPaymentForMonthlyPayment(
                        cpt, new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate()));
            }
        }

        m.put("contractPayments", contractPayments);
        return m;
    }

    public void startPayingViaCreditCardFlow(ContractPaymentTerm cpt, List<ContractPayment> contractPayments, boolean fromErp) {

        Map<String, Object> map = new HashMap<>();
        map.put("trials", 0);
        map.put("reminders", 1);
        map.put("lastExecutionDate", new Date());
        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());

        FlowEventConfig flowEventConfig = flowEventConfigRepository
                .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);
        FlowSubEventConfig flowSubEventConfig = flowSubEventConfigRepository.findByNameAndFlowEventConfig(
                FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA, flowEventConfig);

        FlowProcessorEntity flow = flowProcessorService.createFlowProcessor(
                flowEventConfig, flowSubEventConfig, cpt, map);

        if (startInitialFlow(flow, contractPayments, true, fromErp))
            flowProcessorService.processFlowSubEventConfig(
                    flowProcessorEntityRepository.findOne(flow.getId()));
    }

    public void handleAddConfirmationToDoFromErp(ContractPaymentConfirmationToDo toDo) {

        if (Arrays.asList(ContractStatus.EXPIRED, ContractStatus.CANCELLED)
                .contains(toDo.getContractPaymentTerm().getContract().getStatus()))
            throw new BusinessException("Error: Cannot trigger client paying via credit card flow on cancelled contract");

        if (toDo.getContractPaymentTerm().getContract().isPayingViaCreditCard())
            throw new BusinessException("Contract already flagged as paying via credit card");

        if (flowProcessorEntityRepository.existsByFlowEventConfig_NameAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalse(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card, toDo.getContractPaymentTerm().getContract()))
            throw new BusinessException("Contract has running paying via credit card flow");

        List<ContractPayment> l = getInitialFlowPaymentsForErp(toDo.getContractPaymentTerm().getContract());

        l = l.stream()
                .filter(cp -> toDo.getContractPaymentList().stream()
                        .anyMatch(cw ->
                                cw.getAmount().equals(cp.getAmount()) &&
                                        new LocalDate(cw.getPaymentDate()).toString("yyyy-MM-dd")
                                                .equals(new LocalDate(cp.getDate()).toString("yyyy-MM-dd")) &&
                                        cw.getPaymentType().getId().equals(cp.getPaymentType().getId())
                        )).collect(Collectors.toList());

        l.forEach(cp -> {
            cp.setDescription(toDo.getDescription());
            cp.setAttachments(toDo.getAttachments());
        });

        startPayingViaCreditCardFlow(toDo.getContractPaymentTerm(), l, true);
    }

    public List<ContractPayment> getInitialFlowPaymentsForErp(Contract contract) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        cpt.setForceGenerateDds(true);
        Map<String, Object> m =  new HashMap<>();
        m.put("ignorePostponedDdGenerated", true);
        List<ContractPayment> contractPayments = (List<ContractPayment>) Setup.getApplicationContext()
                .getBean(ContractPaymentTermServiceNew.class)
                .getDefaultDirectDebitPayments(cpt, m)
                .getOrDefault("payments", new ArrayList<>());

        logger.info("contractPayments size: " + contractPayments.size());

        return contractPayments;
    }

    public Date getChangeToPayingViaCcDate(Contract c) {

        AccountingEntityProperty a = Setup.getRepository(AccountingEntityPropertyRepository.class)
                .findByKeyAndOriginAndDeletedFalse(Contract.CHANGE_TO_PAYING_VIA_CREDIT_CARD_DATE, c);
        if (a != null && a.getValue() != null) {
            try {
                return DateUtil.parseDashedDateFormatWithTimeV3(a.getValue());
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        HistorySelectQuery<Contract> q = new HistorySelectQuery<>(Contract.class);
        q.filterBy("id", "=", c.getId());
        q.filterBy("payingViaCreditCard", "=", true);
        q.filterByChanged("payingViaCreditCard");
        q.sortBy("lastModificationDate", false);
        q.setLimit(1);

        List<Contract> l = q.execute();
        Date d = l.isEmpty() ? null : l.get(0).getLastModificationDate();
        logger.info("contract id: " + c.getId() + "; date: " + (d == null ? "NULL" : new LocalDate(d).toString("yyyy-MM-dd")));
        return d;
    }

    // The code from here is shared with IPAM flow
    public void sendEmailForManualSwitchingNationality(ContractPaymentTerm oldCpt, ContractPaymentTerm newCpt) {
        logger.info("old cpt id: " + oldCpt.getId());

        String subject = "Manual Switching Nationality - " + oldCpt.getContract().getClient().getId();

        Map<String, String> parameters = new HashMap<String, String>() {{
            put("old_nationality", oldCpt.getHousemaid().getNationality().getName());
            put("new_nationality", newCpt.getHousemaid().getNationality().getName());
            put("client_id", oldCpt.getContract().getClient().getId().toString());
            put("old_maid_id", oldCpt.getHousemaid().getId().toString());
            put("new_maid_id", newCpt.getHousemaid().getId().toString());
            put("old_cpt_name", oldCpt.getPaymentTermConfig().getName());
            put("new_cpt_name", newCpt.getPaymentTermConfig().getName());
        }};

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "client_paying_via_credit_card_manual_switching_nationality",
                        parameters,
                        Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_CLIENT_PAYING_VIA_CREDIT_CARD_MANUAL_SWITCHING_NATIONALITY_EMAIL_RECEIPTS),
                        subject);
    }

    // ACC-5533
    public void handleSwitchNationalityRequest(
            ContractPaymentTerm cpt, Housemaid newHousemaid, Replacement replacement, SwitchingNationalityService.SwitchingNationalityType type) {
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "handleSwitchNationalityViaPayingCc_" + cpt.getContract().getId() + "_" + new java.util.Date().getTime(),
                        "accounting",
                        "clientPayingViaCreditCardService",
                        "handleSwitchNationalityRequest")
                        .withRelatedEntity("Replacement", replacement.getId())
                        .withParameters(
                                new Class[]{Long.class, Long.class, Long.class, Boolean.class},
                                new Object[]{cpt.getId(),
                                        newHousemaid.getId(),
                                        replacement.getId(),
                                        type.equals(SwitchingNationalityService.SwitchingNationalityType.UPGRADING)})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    public void handleSwitchNationalityRequest(Long cptId, Long newHousemaidId, Long replacementId, Boolean isUpgradingNationality) throws Exception {
        ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(cptId);
        Housemaid newHousemaid = Setup.getRepository(HousemaidRepository.class).findOne(newHousemaidId);
        Replacement replacement = Setup.getRepository(ReplacementRepository.class).findOne(replacementId);

        if (isUpgradingNationality)
            handleUpgradingNationalityRequest(cpt, newHousemaid, replacement);
        else handleDowngradeNationalityRequest(cpt, newHousemaid, replacement);
    }

    // ACC-5533
    private void handleDowngradeNationalityRequest(ContractPaymentTerm cpt, Housemaid newHousemaid, Replacement replacement) throws JsonProcessingException {
        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        DateTime replacementDate = new DateTime();
        // move to new cpt
        ContractPaymentTerm newCpt = switchingNationalityService.createNewCptAfterSwitchingAndMoveOldFlow(cpt, newHousemaid,
                replacementDate, SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING, replacement);
        // disable old paytabs link
        contractPaymentConfirmationToDoService.disablePayTabLinksBySource(
                cpt.getContract(), Arrays.asList(
                        ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card,
                        ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW));

        switchingNationalityService.sendReplacementSuccessMessage(newCpt, newHousemaid);

        DateTime lastPaymentReceived = paymentService
                .getLastReceivedMonthlyPaymentDate(cpt.getContract());
        if (lastPaymentReceived == null ||
                lastPaymentReceived.isBefore(new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay()))
            return;

        // send email to the user and notification to the client
        Double amount = getRefundAmount(cpt, newCpt, replacementDate);
        if (amount <= 0) return;

        switchingNationalityService.sendRefundEmailUponDowngradingNationality(cpt, newHousemaid, amount);
    }

    public Double getRefundAmount(ContractPaymentTerm cpt, ContractPaymentTerm newCpt, DateTime replacementDate) {
        LocalDate d = replacementDate.dayOfMonth().get() ==
                new LocalDate().dayOfMonth().withMaximumValue().getDayOfMonth() ?
                new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue() :
                new LocalDate().dayOfMonth().withMinimumValue();
        AtomicReference<Double> amount = new AtomicReference<>(0.0);
        logger.info("date of payment: " + d.toString("yyyy-MM-dd"));

        contractPaymentService.getUniqueAndSortedMonthlyPayments(
                paymentRepository.getMonthlyPaymentForRefundAfterDownGradeNationality(cpt.getContract(), d.toDate()))
                .forEach(o -> {
                    if (new LocalDate(o[2]).toString("yyyy-MM").equals(replacementDate.toString("yyyy-MM"))) {
                        Double newPrice = (Double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(
                                        newCpt, replacementDate.toLocalDate(), false, new HashMap<String, Object>() {{
                                            put("ignoreFirstMonthPayment", true);
                                        }})
                                .get("amount");

                        int numOfDaysToEOF = switchingNationalityService.getDaysToEoM(replacementDate);
                        int daysCurrentMonth = replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth();

                        if (replacementDate.toString("yyyy-MM")
                                .equals(new LocalDate(cpt.getContract().getStartOfContract()).toString("yyyy-MM"))) {
                            daysCurrentMonth = Days.daysBetween(new LocalDate(cpt.getContract().getStartOfContract()),
                                    new LocalDate().dayOfMonth().withMaximumValue()).getDays() + 1;
                            // newPrice = newPrice / replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth() * daysCurrentMonth;
                        }

                        Double a = ((Double) o[1] / daysCurrentMonth * numOfDaysToEOF) -
                                (newPrice / daysCurrentMonth * numOfDaysToEOF);
                        amount.updateAndGet(v -> Double.valueOf((v + a)));
                        logger.log(Level.INFO, "oldPrice: {0}, newPrice: {1}, numOfDaysToEOF: {2}", new Object[]{o[1], newPrice, numOfDaysToEOF});
                        return;
                    }

                    Double newPrice = (Double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(newCpt, new LocalDate(o[2])).get("amount");
                    logger.log(Level.INFO, "oldPrice: {0}, newPrice: {1}", new Object[]{o[1], newPrice});
                    amount.updateAndGet(v -> Double.valueOf((v + ((Double) o[1] - newPrice))));
                });

        logger.info("amount: " + amount.get());
        return amount.get();
    }

    public Map<String, Object> getDowngradeNationalityPaymentInfoCreditCard(
            ContractPaymentTerm cpt,
            Housemaid newHousemaid,
            DateTime replacementDate) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        Map<String, Object> r = new HashMap<>();
        r.put("payments", new ArrayList<>());
        List<Map<String, Object>> l = new ArrayList<>();
        Map<String, Object> cond = getSwitchNationalitySpecialCasesConditions(cpt.getContract(), replacementDate);

        ContractPaymentTerm newCpt = switchingNationalityService.createNewTermForSwitchingNationalities(
                cpt.getContract(), newHousemaid, cpt, false, replacementDate,
                SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING, true);

        if (!(boolean) cond.get("clientPaidCurrentMonth")) {
            l.add(switchNationalityGetCurrentMonthPayment(replacementDate, cpt, newCpt, false));
        }

        if ((boolean) cond.get("downgradeOnLastDayOfMonth")) {
            l.add(switchNationalityGetNextMonthPayment(replacementDate, newCpt, false));
        }

        // Add all non-monthly payment
        Map<String, Object> nonMonthlyPayment = getNonMonthlyPayment(cpt);
        if (l.isEmpty() && !nonMonthlyPayment.containsKey("nonMonthlyPayment")) return r;

        Map<String, String> parameters = new HashMap<>();
        parameters.put("date", DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                .parseLocalDate((String) l.get(l.size() - 1).get("paymentDate"))
                .dayOfMonth()
                .withMaximumValue()
                .toString("EEEE, MMM dd, yyyy"));
        parameters.put("payments_info", l.size() < 2 && !nonMonthlyPayment.containsKey("nonMonthlyPayment") ? "" :
                switchingNationalityService.getPayingViaCreditCardPaymentsInfo(l));

        if (nonMonthlyPayment.containsKey("nonMonthlyPayment")) {
            parameters.put("payments_info", parameters.get("payments_info") +
                    (l.isEmpty() ? "" : " + ") +
                    nonMonthlyPayment.get("nonMonthlyPaymentDescription"));
            l.addAll((List<Map<String, Object>>) nonMonthlyPayment.get("nonMonthlyPayment"));
        }
        parameters.put("total_amount", String.valueOf(Double.valueOf(l.stream().mapToDouble(p -> (Double) p.get("amount")).sum()).intValue()));

        r.put("parameters", parameters);
        r.put("payments", l);
        return r;
    }

    public Map<String, Object> getSwitchNationalitySpecialCasesConditions(Contract contract, DateTime replacementDate) {

        DateTime lastPaymentReceived = paymentService.getLastReceivedMonthlyPaymentDate(contract);

        boolean clientPaidCurrentMonth = lastPaymentReceived != null &&
                lastPaymentReceived.plusHours(1)
                        .isAfter(new DateTime().dayOfMonth().withMinimumValue().withTimeAtStartOfDay());
        boolean downgradeOnLastDayOfMonth = replacementDate.dayOfMonth().get() == new LocalDate().dayOfMonth().withMaximumValue().getDayOfMonth() &&
                (lastPaymentReceived == null || lastPaymentReceived.plusHours(1)
                        .isBefore(replacementDate.plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay()));

        logger.info("contract id: " + contract.getId() +
                "; clientPaidCurrentMonth: " + clientPaidCurrentMonth +
                "; downgradeOnLastDayOfMonth: " + downgradeOnLastDayOfMonth);

        return new HashMap<String, Object>() {{
            put("clientPaidCurrentMonth", clientPaidCurrentMonth);
            put("downgradeOnLastDayOfMonth", downgradeOnLastDayOfMonth);
        }};
    }

    private Map<String, Object> switchNationalityGetCurrentMonthPayment(DateTime replacementDate, ContractPaymentTerm oldCpt, ContractPaymentTerm newCpt, boolean isUpgrade) {

        double oldPriceRate = (Double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(oldCpt, replacementDate.toLocalDate()).get("amount") /
                replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth();
        Map<String, Object> newPrice = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(newCpt, replacementDate.toLocalDate());
        double newPriceRate = (Double) newPrice.get("amount") /
                replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth();
        int numOfDaysToEOF = switchingNationalityService.getDaysToEoM(replacementDate);
        int passedDays = new LocalDate().dayOfMonth().withMaximumValue().dayOfMonth().get() - numOfDaysToEOF;
        // ACC-7399
        if (replacementDate.toString("yyyy-MM")
                .equals(new LocalDate(oldCpt.getContract().getStartOfContract()).toString("yyyy-MM"))) {
            passedDays -= Math.max((new LocalDate(oldCpt.getContract().getStartOfContract()).getDayOfMonth() - 1), 0);
        }

        Double amount = (oldPriceRate * passedDays) + (newPriceRate * numOfDaysToEOF);
        logger.info("cpt id: " + oldCpt.getId() +
                "; oldPriceRate: " + oldPriceRate +
                "; passedDays: " + passedDays +
                "; newPriceRate: " + newPriceRate +
                "; numOfDaysToEOF: " + numOfDaysToEOF);
        Map<String, Object> m = new HashMap<>();
        m.put("paymentType", new HashMap<String, Object>() {{
            put("id", Setup.getItem("TypeOfPayment", "monthly_payment").getId());
        }});

        DateTime startContract = new DateTime(oldCpt.getContract().getStartOfContract());
        m.put("paymentDate", startContract.toString("yyyy-MM").equals(replacementDate.toString("yyyy-MM")) ?
                startContract.toString("yyyy-MM-dd 00:00:00") :
                replacementDate.dayOfMonth().withMinimumValue().toString("yyyy-MM-dd 00:00:00"));
        m.put("amount", Math.floor(amount));
        m.put("affectedByAdditionalDiscount", newPrice.get("affectedByAdditionalDiscount"));
        m.put("includeWorkerSalary", newPrice.get("includeWorkerSalary"));
        m.put("workerSalary", newPrice.get("workerSalary"));
        m.put("moreAdditionalDiscount", newPrice.getOrDefault("moreAdditionalDiscount", 0.0));
        m.put("includeUpgradingFee", isUpgrade);
        return m;
    }

    private Map<String, Object> switchNationalityGetNextMonthPayment(DateTime replacementDate, ContractPaymentTerm newCpt, boolean isUpgrade) {
        logger.info("contract id: " + newCpt.getContract().getId());

        Map<String, Object> newPrice = calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(
                newCpt, replacementDate.plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay().toLocalDate());
        Map<String, Object> m = new HashMap<>();
        m.put("paymentType", new HashMap<String, Object>() {{
            put("id", Setup.getItem("TypeOfPayment", "monthly_payment").getId());
        }});

        m.put("paymentDate", replacementDate.plusMonths(1).dayOfMonth().withMinimumValue().toString("yyyy-MM-dd 00:00:00"));
        m.put("amount", newPrice.get("amount"));
        m.put("affectedByAdditionalDiscount", newPrice.get("affectedByAdditionalDiscount"));
        m.put("includeWorkerSalary", newPrice.get("includeWorkerSalary"));
        m.put("workerSalary", newPrice.get("workerSalary"));
        m.put("moreAdditionalDiscount", newPrice.getOrDefault("moreAdditionalDiscount", 0.0));
        m.put("includeUpgradingFee", isUpgrade);

        return m;
    }

    private void handleUpgradingNationalityRequest(ContractPaymentTerm cpt, Housemaid newHousemaid, Replacement replacement) throws Exception {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        ContractPaymentTerm newCpt = switchingNationalityService.createNewCptAfterSwitchingAndMoveOldFlow(cpt, newHousemaid,
                new DateTime(), SwitchingNationalityService.SwitchingNationalityType.UPGRADING, replacement);
        logger.log(Level.INFO, "newCpt id: {0}", newCpt.getId());

        contractPaymentConfirmationToDoService.disablePayTabLinksBySource(
                cpt.getContract(), Arrays.asList(
                        ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card,
                        ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW));

        switchingNationalityService.sendReplacementSuccessMessage(newCpt, newHousemaid);
    }

    public Map<String, Object> getUpgradeNationalityPaymentInfoCreditCard(ContractPaymentTerm cpt, Housemaid newHousemaid, DateTime replacementDate) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());
        ContractPaymentTerm newCpt = switchingNationalityService.createNewTermForSwitchingNationalities(
                cpt.getContract(), newHousemaid, cpt, false, replacementDate,
                SwitchingNationalityService.SwitchingNationalityType.UPGRADING, true);

        Map<String, Object> r = new HashMap<>();
        Map<String, String> parameters = new HashMap<>();
        parameters.put("payments_info", "");
        Long paymentTypeId = Setup.getItem("TypeOfPayment", "upgrading_nationality").getId();
        List<Map<String, Object>> l = new ArrayList<>();
        Map<String, Object> m;
        Map<String, Object> firstPayment = new HashMap<>();

        List<Object[]> receivedPayments = contractPaymentService.getUniqueAndSortedMonthlyPayments(
                paymentRepository.findMonthlyPaymentByStatusAndDateOfPaymentGreaterThanEqual(
                        cpt.getContract(), PaymentStatus.RECEIVED,
                        replacementDate.dayOfMonth().withMinimumValue().withTimeAtStartOfDay().toDate()));

        Map<String, Map<String, Object>> groupByAmount = new LinkedHashMap<>();
        double lastAmount = -1D;
        String key = null;
        for (Object[] p : receivedPayments) {
            Double oldPrice = (Double) p[1];
            if (new LocalDate(p[2]).toString("yyyy-MM").equals(replacementDate.toString("yyyy-MM"))) {
                Double newPrice = (Double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(
                                newCpt, replacementDate.toLocalDate(), false, new HashMap<String, Object>() {{
                                    put("ignoreFirstMonthPayment", true);
                                }})
                        .get("amount");

                int replacementMonthDays = replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth();
                int numOfDaysToEOF = switchingNationalityService.getDaysToEoM(replacementDate);

                if (replacementDate.toString("yyyy-MM")
                        .equals(new LocalDate(cpt.getContract().getStartOfContract()).toString("yyyy-MM"))) {
                    replacementMonthDays = Days.daysBetween(new LocalDate(cpt.getContract().getStartOfContract()),
                            new LocalDate().dayOfMonth().withMaximumValue()).getDays() + 1;
                    // newPrice = newPrice / replacementDate.dayOfMonth().withMaximumValue().getDayOfMonth() * replacementMonthDays;
                }

                logger.log(Level.INFO, "oldPrice: {0}, newPrice: {1}, replacementMonthDays {2}, numOfDaysToEOF {3}",
                        new Object[]{oldPrice, newPrice, replacementMonthDays, numOfDaysToEOF});

                double amount = Math.floor((newPrice - oldPrice) / replacementMonthDays * numOfDaysToEOF);

                if (amount > 0) {
                    firstPayment.put("paymentType", new HashMap<String, Object>() {{
                        put("id", paymentTypeId);
                    }});
                    firstPayment.put("paymentDate", replacementDate.toString("yyyy-MM-dd 00:00:00"));
                    firstPayment.put("amount", amount);
                    l.add(firstPayment);
                }
                continue;
            }

            double newPrice = (double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(newCpt, new LocalDate(p[2])).get("amount");
            logger.log(Level.INFO, "oldPrice: {0}, newPrice: {1}", new Object[]{oldPrice, newPrice});

            m = new HashMap<>();
            LocalDate d = new LocalDate(p[2]);
            m.put("paymentType", new HashMap<String, Object>() {{
                put("id", paymentTypeId);
            }});
            m.put("paymentDate", d.toString("yyyy-MM-dd 00:00:00"));
            double amount = Math.floor(newPrice - oldPrice);
            m.put("amount", amount);
            l.add(m);

            if (amount == lastAmount && key != null) {
                Map<String, Object> a = groupByAmount.get(key);
                a.put("lastMonth", d.toString("MMM"));
                a.put("count", (Integer) a.get("count") + 1);
                groupByAmount.put(key, a);
            } else {
                key = d.toString("yyyy-MM");
                Map<String, Object> a = new HashMap<>();
                a.put("firstMonth", d.toString("MMM"));
                a.put("lastMonth", d.toString("MMM"));
                a.put("amount", amount);
                a.put("count", 1);
                groupByAmount.put(key, a);
            }
            lastAmount = amount;
        }

        Map<String, Object> cond = getSwitchNationalitySpecialCasesConditions(cpt.getContract(), replacementDate);
        if (!(boolean) cond.get("clientPaidCurrentMonth")) {
            firstPayment = switchNationalityGetCurrentMonthPayment(replacementDate, cpt, newCpt, true);
            l.add(firstPayment);
        }

        String nextMonthInfo = "";
        if ((boolean) cond.get("downgradeOnLastDayOfMonth")) {
            Map<String, Object> nextMonthPayment = switchNationalityGetNextMonthPayment(replacementDate, newCpt, true);
            l.add(nextMonthPayment);
            nextMonthInfo = switchingNationalityService.getPayingViaCreditCardPaymentsInfo(Collections.singletonList(nextMonthPayment));
        }

        Map<String, Object> nonMonthlyPayment = getNonMonthlyPayment(cpt);
        if (l.size() > 1 || nonMonthlyPayment.containsKey("nonMonthlyPayment")) {
            if (receivedPayments.isEmpty()) {
                parameters.put("payments_info", switchingNationalityService.getPayingViaCreditCardPaymentsInfo(l));
            } else {
                logger.info("groupByAmount: " + groupByAmount.entrySet());
                parameters.put("payments_info",
                        switchingNationalityService.getPayingViaCreditCardPaymentsInfo(firstPayment, groupByAmount));
                if (!nextMonthInfo.isEmpty())
                    parameters.put("payments_info", parameters.get("payments_info") + " + " + nextMonthInfo);
            }
        }

        LocalDate ped = l.isEmpty() ? new LocalDate(cpt.getContract().getPaidEndDate()) :
                DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                        .parseLocalDate((String) l.get(l.size() - 1).get("paymentDate"))
                        .dayOfMonth()
                        .withMaximumValue();
        parameters.put("date", ped.toString("EEEE, MMM dd, yyyy"));

        if (nonMonthlyPayment.containsKey("nonMonthlyPayment")) {
            parameters.put("payments_info", parameters.get("payments_info") +
                    (l.isEmpty() ? "" : " + ") +
                    nonMonthlyPayment.get("nonMonthlyPaymentDescription"));
            l.addAll((List<Map<String, Object>>) nonMonthlyPayment.get("nonMonthlyPayment"));
        }
        parameters.put("total_amount", String.valueOf(Double.valueOf(l.stream().mapToDouble(p -> (Double) p.get("amount")).sum()).intValue()));

        r.put("payments", l);
        r.put("parameters", parameters);
        return r;
    }

    public void setReplacementSuccessMessageParameters(
            ContractPaymentTerm cpt,
            Housemaid newHousemaid,
            DateTime lastPayment,
            Map<String, String> p,
            Map<String, AppAction> c) {

        logger.info("cpt id: " + cpt.getId());

        ContractPaymentConfirmationToDo toDo = null;

        if (lastPayment == null || lastPayment.plusHours(1).isBefore(
                new DateTime().plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay())) {

            if (cpt.getContract().isPayingViaCreditCard()) {
                toDo = createTodoIfNotExists(cpt,  new LocalDate(lastPayment).plusMonths(1));
            } else {
                try {
                    toDo = Setup.getApplicationContext()
                                    .getBean(AfterCashFlowService.class)
                                    .addConfirmationTodoForPayment(cpt.getContract());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        if (toDo != null) {
            String link = shortener.shorten(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE)
                    + "/modules/accounting/paytabs/#!/home?uid=" + cpt.getContract().getUuid());
            int amount = toDo.getTotalAmount().intValue();

            p.put("amount", String.valueOf(amount));
            p.put("todoUuid", toDo.getUuid());
            p.put("paying_via_credit_card_sms", link);

            String cmsText = TemplateUtil.compileTemplate(
                    TemplateUtil.getTemplate(CcAppCmsTemplate.CC_PAYMENT_IN_ADVANCE_AFTER_REPLACEMENT.toString()),
                    null, new HashMap<String, String>() {{

                        put("date", new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toString("yyyy-MM-dd"));
                        put("amount", String.valueOf(amount));
                    }});

            c.put("link", Setup.getApplicationContext()
                    .getBean(FlowProcessorMessagingService.class)
                    .getPaytabsAction(p, cpt.getContract(), AppActionType.LINK,
                            "link", cmsText, "/replace_switch_nationality_pay_notification"));

            p.put("paid_end_date", new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toString("yyyy-MM-dd"));
            p.put("todoId", String.valueOf(toDo.getId()));
            p.put("link", "@link@");
        } else {
            p.put("amount", String.valueOf(((Double) calculateDiscountsWithVatService.getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, lastPayment.plusMonths(1)
                    .withDayOfMonth(1)
                    .withTimeAtStartOfDay()
                    .toLocalDate())

                    .get("amount"))
                    .intValue()));
            p.put("paid_end_date", new LocalDate(cpt.getContract().getPaidEndDate()).toString("yyyy-MM-dd"));
        }

        p.put("new_nationality", StringUtils.getHousemaidNationality(newHousemaid.getNationality().getName()));
    }

    private Map<String, Object> getNonMonthlyPayment(ContractPaymentTerm cpt) {

        ContractPaymentConfirmationToDo todo = contractPaymentConfirmationToDoRepository
                .findFirstByContractPaymentTerm_ContractAndSourceInAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
                cpt.getContract(), Arrays.asList(
                        ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card,
                                ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW));
        if (todo == null || todo.getContractPaymentList().isEmpty()) return new HashMap<>();

        StringBuilder b = new StringBuilder();
        List<Map<String, Object>> l = todo.getContractPaymentList()
                .stream()
                .filter(c -> !c.getPaymentType().getCode().equals("monthly_payment"))
                .map(c -> {
                    Map<String, Object> m = new HashMap<>();
                    m.put("paymentType", new HashMap<String, Object>() {{
                        put("id", c.getPaymentType().getId());
                    }});
                    m.put("paymentDate", new DateTime(c.getPaymentDate()).toString("yyyy-MM-dd 00:00:00"));
                    m.put("amount", c.getAmount());
                    b.append(b.toString().isEmpty() ? "" : " + ")
                            .append("AED ")
                            .append(c.getAmount().intValue())
                            .append(" ")
                            .append(c.getDescription() == null || c.getDescription().isEmpty() ?
                                    c.getPaymentType().getName() :
                                    c.getDescription());
                    return m;
                })
                .collect(Collectors.toList());

        if (l.isEmpty()) return new HashMap<>();

        Map<String, Object> m = new HashMap<String, Object>() {{
            put("nonMonthlyPayment", l);
            put("nonMonthlyPaymentDescription", b.toString());
        }};

        logger.info("payments: " + m.entrySet());
        return m;
    }

    public boolean skipReminderFlow(FlowProcessorEntity entity, ContractPaymentTerm cpt) {
        logger.info("entity id: " + (entity == null ? "NULL" : entity.getId()) + "; cpt id: " + cpt.getId());
        if ((entity != null && !entity.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER)) ||
                !ContractService.isEligibleForTokenizationViaContract(cpt.getContract()) ||
                cpt.getSourceId() == null) return false;

        if (!paymentService.nextMonthRecurringAndMatchTokenAmount(cpt)) return false;

        if (validateStartExpiryFlow(cpt)) {
            if (entity != null) {
                entity.setCurrentSubEvent(flowSubEventConfigRepository.findByNameAndFlowEventConfig_Name(
                        FlowSubEventConfig.FlowSubEventName.EXPIRED_CARD, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card));

                if (new DateTime().isAfter(new DateTime(entity.getContract().getPaidEndDate()))) {
                    entity.setIncrementedTrials(entity.getCurrentSubEvent().getMaxTrials());
                    entity.setIncrementedReminders(entity.getCurrentSubEvent().getMaxReminders());
                }
            }

            logger.info("start Expiry flow");
            return false;
        }

        return true;
    }

    public boolean skipExceedsCardLimitAndInsufficientFundsFlow(FlowProcessorEntity entity) {
        logger.info("flow processor id: " + entity.getId() );
        if (entity.getContractPaymentConfirmationToDo() == null ||
                entity.getContractPaymentConfirmationToDo().getContractPaymentList().isEmpty() ||
                entity.getContractPaymentConfirmationToDo().getContractPaymentList().get(0).getGeneratedPaymentId() == null) return false;
        Payment payment = paymentRepository.findOne(entity.getContractPaymentConfirmationToDo()
                .getContractPaymentList().get(0).getGeneratedPaymentId());

        if(payment == null || payment.getStatus().equals(PaymentStatus.RECEIVED)) return false;

        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("targetBean", "=", "clientPayingViaCreditCardService");
        query.filterBy("targetMethod", "=", "reTryCaptureRecurringPayment");
        query.filterBy("relatedEntityType", "=", payment.getEntityType());
        query.filterBy("relatedEntityId", "=", payment.getId());
        query.filterBy("creationDate", ">=", new LocalDate().toDate());

        List<BackgroundTask> result = query.execute();

        logger.info("result size: " + result.size());

        if (result.isEmpty()) {
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .create(new BackgroundTask.builder(
                            "reTryCaptureRecurringPayment_" + payment.getId(),
                            "accounting",
                            "clientPayingViaCreditCardService",
                            "reTryCaptureRecurringPayment")
                            .withRetryDelay(30 * 60 * 1000L)
                            .withRelatedEntity(payment.getEntityType(), payment.getId())
                            .withParameters(new Class[]{Long.class},
                                    new Object[]{payment.getId()})
                            .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                            .build());
            return true;
        }

       return !Arrays.asList(BackgroundTaskStatus.Failed, BackgroundTaskStatus.Finished)
               .contains(result.get(0).getStatus());
    }

    public void createCaptureRecurringPaymentBGT(Payment payment) {

        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("targetBean", "=", "clientPayingViaCreditCardService");
        query.filterBy("targetMethod", "=", "captureRecurringPayment");
        query.filterBy("relatedEntityType", "=", payment.getEntityType());
        query.filterBy("relatedEntityId", "=", payment.getId());

        List<BackgroundTask> result = query.execute();
        if (!result.isEmpty()) return;

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "createCaptureRecurringPaymentBGT" + payment.getId(),
                        "accounting",
                        "clientPayingViaCreditCardService",
                        "captureRecurringPayment")
                        .withRetryDelay(30 * 60 * 1000L)
                        .withRelatedEntity(payment.getEntityType(), payment.getId())
                        .withParameters(new Class[]{Long.class},
                                new Object[]{payment.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    public void captureRecurringPayment(Long paymentId) throws Exception {
        Payment p = paymentRepository.findOne(paymentId);
        ContractPaymentTerm cpt = p.getContract().getActiveContractPaymentTerm();

        Map<String, Object> m = captureRecurringPayment(p, cpt);
        EPaymentTransaction ePaymentTransaction = (EPaymentTransaction) m.get("ePaymentTransaction");
        // if the result transaction status not Pending or has error then the operation failed and has error will contain the proper message
        if (Arrays.asList(ETransactionStatus.PENDING, ETransactionStatus.SUCCESS)
                .contains(ePaymentTransaction.getTransactionStatus()) &&
            ePaymentTransaction.getHasError() == null) return;

        List<FlowSubEventConfig> subEventConfigs = ePaymentTransaction.getErrorCode() != null ?
                checkoutErrorCodeForSubFlowRepository.findSubEventByErrorCod(ePaymentTransaction.getErrorCode()) :
                new ArrayList<>();

        if(subEventConfigs.isEmpty() &&
                ePaymentTransaction.getHasError() != null &&
                ePaymentTransaction.getErrorCode() != null) {
            addCodeToPickListItemAuto(ePaymentTransaction.getErrorCode());
        }

        FlowSubEventConfig.FlowSubEventName subEventName = subEventConfigs.isEmpty() ?
                FlowSubEventConfig.FlowSubEventName.CC_OTHER_ISSUES : subEventConfigs.get(0).getName();

        logger.info("subEvent: " + subEventName);
        if (Arrays.asList(
                FlowSubEventConfig.FlowSubEventName.EXPIRED_CARD,
                        FlowSubEventConfig.FlowSubEventName.ACCOUNT_ISSUE)
                .contains(subEventName)) {
            Setup.getApplicationContext()
                    .getBean(ContractPaymentTermServiceNew.class)
                    .deleteCreditCardToken(cpt, false);
        }

        startPayingViaRecurringSubFlows(cpt,
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                subEventName,
                (ContractPaymentConfirmationToDo) m.get("todo"),
                p);
    }

    public void reTryCaptureRecurringPayment(Long paymentId) {
        Payment p = paymentRepository.findOne(paymentId);
        captureRecurringPayment(p, p.getContract().getActiveContractPaymentTerm());
    }

    public Map<String, Object> captureRecurringPayment(Payment p, ContractPaymentTerm cpt) {
        logger.info("cpt id: " + cpt.getId() + "; payment id: " + p.getId());
        ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoService
                .createToDoForRecurringPaymentIfNotExists(p, cpt);

         return new HashMap<String, Object>() {{
             put("ePaymentTransaction", accountingEPaymentService.captureRecurringPayment(cpt.getSourceId(), toDo));
             put("toDo", toDo);
         }};
     }

    public void handleFailedErpCaptureRecurringPayment() {

        try {
            SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
            query.filterBy("targetBean", "=", "clientPayingViaCreditCardService");
            query.filterBy("targetMethod", "=", "captureRecurringPayment");
            query.filterBy("status", "=", BackgroundTaskStatus.Failed);
            query.filterBy("creationDate", ">=", new LocalDate().getDayOfMonth() == 1 ?
                    new LocalDate().toDate() :
                    new LocalDateTime().minusHours(24).toDate());
            List<BackgroundTask> result = query.execute();
            if (result.isEmpty()) return;

            result.forEach(b -> {
                try {
                    Payment p = paymentRepository.findOne(b.getRelatedEntityId());
                    ContractPaymentTerm cpt = p.getContract().getActiveContractPaymentTerm();
                    logger.info("p id: " + p.getId() + "; cpt id: " + cpt.getId() + "; BG Task id: " + b.getId());

                if (flowProcessorEntityRepository.existsFlowByRelatedTodo(cpt.getContract(),
                        FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                        FlowProcessorService.recurringFailureFlows,
                        p.getId())) return;

                    startPayingViaRecurringSubFlows(cpt, FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                            FlowSubEventConfig.FlowSubEventName.CC_OTHER_ISSUES, null, p);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean validateStartExpiryFlow(ContractPaymentTerm cpt) {

        return (Days.daysBetween(new LocalDate(), (LocalDate) cpt.getSourceInfo().get("expiryDate")).getDays()) < 30;
    }

    public void sendNewMessageForTokenDeletionSubEvent(ContractPaymentTerm cpt) {
        logger.info("cpt id: " + cpt.getId());
        FlowProcessorEntity f = flowProcessorService.getRunningFlow(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                cpt.getContract(),
                null);
        if (f == null) {
            int days = Integer.parseInt(Setup.getRepository(FlowEventConfigRepository.class)
                    .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card)
                        .getTagValue("monthly_reminder_paying_cc_start_before_x_days").getValue()) + 2;
            List<FlowProcessorEntity> l = flowProcessorEntityRepository.findStoppedMonthlyReminder(cpt.getContract(),
                    new LocalDate().minusDays(days).toDate());
            if (l.isEmpty()) return;
            f = l.get(0);
        }
        logger.info("f id: " + f.getId());

        Payment p = f.getContractPaymentConfirmationToDo() != null &&
                !f.getContractPaymentConfirmationToDo().getContractPaymentList().isEmpty() &&
                f.getContractPaymentConfirmationToDo().getContractPaymentList().get(0).getGeneratedPaymentId() != null ?
                paymentRepository.findOne(f.getContractPaymentConfirmationToDo().getContractPaymentList().get(0).getGeneratedPaymentId()) :
                null;

        FlowProcessorEntity tokenDeletedFlow = startPayingViaRecurringSubFlows(cpt,
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                FlowSubEventConfig.FlowSubEventName.TOKEN_DELETED,
                f.getContractPaymentConfirmationToDo(), p);

        if (tokenDeletedFlow == null) return;

        flowProcessorService.processFlowSubEventConfig(tokenDeletedFlow);
    }

    public void sendEmailsForCodeIssueNotRelatedToSubEvent() {

        List<PicklistItem> errorCodes = checkoutErrorCodeForSubFlowRepository.findByErrorCodeIsNotNullAndSubEventIsNull();
        if (errorCodes.isEmpty()) return;
        logger.info("errorCodes size: " + errorCodes.size());

        try {
            CheckOutErrorCodeIssueReport report = new CheckOutErrorCodeIssueReport(errorCodes);
            Map<String, String> parameters = new HashMap<>();
            parameters.put("html_table", report.render());
            parameters.put("card_payments_Issues_management_screen_link",
                    shortener.shorten(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) +
                            "#!/" +
                            "accounting/recurring-cc-payments-issues-mgmt"));
            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff(
                            "credit_card_unknown_error_codes",
                            parameters,
                            Setup.getParameter(Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_CREDIT_CARD_UNKNOWN_ERROR_CODES_EMAIL_RECEIPTS),
                                    "New Recurring Card Payments Issues");

        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean erpAllowedRecurring() {
        return Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_ACC_CREDIT_CARD_TOKENIZATION_ENABLED)
                .equalsIgnoreCase("true") &&
                Enum.valueOf(EPaymentProvider.class, Setup.getCoreParameter(CoreParameter.EPAYMENT_DEFAULT_PROVIDER).toUpperCase())
                        .equals(EPaymentProvider.CHECKOUT);
    }

    public void addCodeToPickListItemAuto(String code) {
        PicklistItem picklistItem = PicklistHelper.getItemNoException(AccountingModule.PICKLIST_CREDIT_CARD_ERROR_CODES, code);

        if (picklistItem != null) return;

        logger.info("code: " + code);
        picklistItem = new PicklistItem();
        picklistItem.setCode(code);
        picklistItem.setName(code);
        picklistItem.setList(Setup.getRepository(PicklistRepository.class)
                                    .findByCode(AccountingModule.PICKLIST_CREDIT_CARD_ERROR_CODES));

        Setup.getRepository(PicklistItemRepository.class).save(picklistItem);
        logger.info("picklistItem code: " + picklistItem.getCode());
    }

    public void removeRecurringFlagAndCreateToDo(Payment payment, ContractPaymentTerm cpt) {
        logger.info("payment id: " + payment.getId() );

        List<ContractPaymentConfirmationToDo> l = contractPaymentConfirmationToDoRepository.findToDoForRecurringCaptureByPaymentId(
                payment.getId(), getPayingViaCcSource(cpt.getContract()));

        if (l.isEmpty()) {
            l = contractPaymentConfirmationToDoRepository.findOnlineCardTodosBySource(
                    cpt.getContract().getId(), AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE,
                    payment.getDateOfPayment(), getPayingViaCcSource(cpt.getContract()));
        }

        if (!l.isEmpty()) {
            stopFailureFlowAndStartOnlineReminder(l.get(0), payment);
        } else {
            paymentService.createToDoIfNotExists(payment, cpt, getPayingViaCcSource(cpt.getContract()));
        }

        payment.setRecurring(false);
        if (payment.getStatus().equals(PaymentStatus.PDC)) {
            payment.setStatus(PaymentStatus.PRE_PDP);
        }
        paymentService.updatePaymentSilent(payment);
    }

    private void stopFailureFlowAndStartOnlineReminder(ContractPaymentConfirmationToDo t, Payment payment) {
        logger.info("todo id: " + t.getId());

        Setup.getApplicationContext()
                .getBean(UnpaidOnlineCreditCardPaymentService.class)
                .createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
                        t.getContractPaymentList().stream()
                                .filter(w -> PaymentHelper.isMonthlyPayment(w.getPaymentType()))
                                .map(w -> {
                                    ContractPayment cp = w.initContractPaymentProps();
                                    cp.setAmount(payment.getAmountOfPayment());
                                    if (Arrays.asList(PaymentStatus.PDC, PaymentStatus.PRE_PDP)
                                            .contains(payment.getStatus())) {
                                        cp.setGeneratedPaymentId(payment.getId());
                                        w.setGeneratedPaymentId(null);
                                    }
                                    return cp;
                                }).collect(Collectors.toList()),
                        t.getContractPaymentTerm(), payment.getDateOfPayment());

        t.setDisabled(true);
        contractPaymentConfirmationToDoRepository.silentSave(t);

        flowProcessorEntityRepository.findByContractPaymentConfirmationToDo(t)
                .forEach(f -> {
                    if (f.isStopped()) return;
                    logger.info("flow id: " + f.getId());
                    f.setStopped(true);
                    flowProcessorEntityRepository.silentSave(f);
        });
    }

    public ContractPaymentConfirmationToDo.Source getPayingViaCcSource(Contract c) {
        return c.isOneMonthAgreement() ?
                ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT :
                ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card;
    }

    public void applyAfterProcessFlowSubEventConfig(FlowProcessorEntity entity, Map<String, Object> m) {
        // Start Extension Flow if got termination message and ExtensionFlow is active for this contract
        if (m.containsKey("startExtensionFlow") && (boolean) m.get("startExtensionFlow")) {
            extensionFlowService.startExtensionFlow(entity.getContractPaymentTerm(), entity);
        }
    }

    public boolean handleScheduledContractForTermination(FlowProcessorEntity entity, Map<String, Object> m) {
        if (extensionFlowService.isEligibleForExtensionFlow(entity)) {
            m.put("startExtensionFlow", true);
            m.put("scheduledDateOfTermination", new LocalDate(entity.getContract().getPaidEndDate()).isAfter(new LocalDate()) ?
                    entity.getContract().getPaidEndDate() :
                    new Date());
            return false;
        }

        return true;
    }

    // ACC-9222
    public List<ContractPayment> handleCurrentPaymentsAlreadyCreated(ContractPaymentTerm cpt, List<ContractPayment> contractPayments) {

        if (contractPayments.isEmpty()) return contractPayments;
        logger.info("contractPayments size" + contractPayments.size());

        DateTime contractStartDate = new DateTime(cpt.getContract().getStartOfContract());
        if (!new DateTime().toString("yyyy-MM").equals(contractStartDate.toString("yyyy-MM"))) {
            return contractPayments;
        }

        // Include All Payments matched to the flow
        logger.info("contractPayments in first Month of contract size: " + contractPayments.size());
        ContractPaymentTypeRepository contractPaymentTypeRepository = Setup.getRepository(ContractPaymentTypeRepository.class);
        for (ContractPaymentType type : cpt.getContractPaymentTypes()) {
            if (!PaymentHelper.isMonthlyPayment(type.getType()) && type.getStartsOn().equals(0)) {
                contractPayments.add(type.generateContractPayment(contractStartDate));
                if (!type.isPostponedDdGenerated()) {
                    type.setPostponedDdGenerated(true);
                    contractPaymentTypeRepository.save(type);
                }
            }
        }

        return contractPayments;
    }

    // ACC-9222
    public void handleFuturePaymentsAlreadyCreated(ContractPaymentTerm cpt, List<DirectDebit> dds, List<ContractPayment> contractPayments) {

        if (dds == null || dds.isEmpty() || contractPayments.isEmpty()) return;
        logger.info("dds size: " + dds.size());

        // 1-Filter just DDAs
        // 2-Collecting all contract Pyamnets in one list
        // 3-Filter Payments non-monthly, in the future and not included in new initial flow
        List<ContractPayment> nonMatchedPaments = dds.stream()
                .filter(dd -> DirectDebitCategory.A.equals(dd.getCategory()))
                .flatMap(dd -> dd.getContractPayments().stream())
                .filter(cp1 -> !PaymentHelper.isMonthlyPayment(cp1.getPaymentType()) &&
                        new DateTime(cp1.getDate()).isAfter(new DateTime().dayOfMonth().withMaximumValue()) &&
                        contractPayments.stream()
                                .noneMatch(cp2 ->
                                        cp2.getAmount().equals(cp1.getAmount()) &&
                                                cp2.getPaymentType().getCode().equals(cp1.getPaymentType().getCode()) &&
                                                new LocalDate(cp2.getDate()).toString("yyyy-MM-dd")
                                                        .equals(new LocalDate(cp1.getDate()).toString("yyyy-MM-dd"))))
                .collect(Collectors.toList());

        // 4-Generate The future payments as Plan
        Map<String, Object> m = new HashMap<>();
        m.put("isPayingViaCreditCard", true);
        Setup.getApplicationContext()
                .getBean(DirectDebitGenerationPlanService.class)
                .generatePlansForFutureContractPayments(cpt, nonMatchedPaments, m);
    }
}