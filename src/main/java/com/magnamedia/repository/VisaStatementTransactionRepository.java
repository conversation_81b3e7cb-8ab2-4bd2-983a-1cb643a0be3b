package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.VisaStatement;
import com.magnamedia.entity.VisaStatementTransaction;
import com.magnamedia.extra.VisaStatementTransactionType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface VisaStatementTransactionRepository extends BaseRepository<VisaStatementTransaction> {
    VisaStatementTransaction findTopByStatementAndFinished(VisaStatement statement, <PERSON>olean finished);
    List<VisaStatementTransaction> findByStatement(VisaStatement statement);

    VisaStatementTransaction findTopByStatementAndTypeNotAndFinishedFalse(
            VisaStatement statement, VisaStatementTransactionType t);

    List<VisaStatementTransaction> findByStatementAndTypeAndFinishedFalse(
            VisaStatement statement, VisaStatementTransactionType t);

    @Query("select new map(v.id as visaStatementTransactionId) " +
            "from VisaStatementTransaction v " +
            "where v.statement = ?1 and v.type = ?2 and v.finished = false and v.fromBucket is not null and " +
                "v.expense is not null and v.description is not null")
    List<Map<String, Object>> findRecordsToBeConfirmed(
            VisaStatement statement, VisaStatementTransactionType type);

    @Query("select t from VisaStatementTransaction t " +
            "where t.type = ?1 and t.finished = false")
    List<VisaStatementTransaction> findByTypeAndFinishedFalse(VisaStatementTransactionType type);

    @Query("select t from VisaStatementTransaction t " +
            "where t.type = ?1 and t.referenceNumber = ?2 and t.finished = false")
    List<VisaStatementTransaction> findByTypeAndReferenceNumberAfterAndFinishedFalse(
            VisaStatementTransactionType type, String referenceNumber);

    @Query("select t from VisaStatementTransaction t " +
            "where t.type = ?1 and t.amount in ?2")
    List<VisaStatementTransaction> findByTypeAndAmountsInAndFinishedFalse(
            VisaStatementTransactionType type, List<Double> amounts);
}